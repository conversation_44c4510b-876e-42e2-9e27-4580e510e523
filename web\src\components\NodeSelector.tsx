import React, { useState, useEffect } from 'react';
import { Select, message, Spin } from 'antd';
import { DatabaseOutlined } from '@ant-design/icons';
import { frpsNodesAPI } from '@/utils/api';

const { Option } = Select;

interface FrpsNode {
  id: number;
  name: string;
  description: string;
  host: string;
  port: number;
  admin_port: number;
  status: 'online' | 'offline' | 'error' | 'unknown';
  is_active: boolean;
}

interface NodeSelectorProps {
  value?: number;
  onChange?: (nodeId: number, node: FrpsNode) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
  allowClear?: boolean;
  showStatus?: boolean;
}

const NodeSelector: React.FC<NodeSelectorProps> = ({
  value,
  onChange,
  placeholder = "请选择节点",
  style,
  disabled = false,
  allowClear = true,
  showStatus = true,
}) => {
  const [nodes, setNodes] = useState<FrpsNode[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchNodes();
  }, []);

  const fetchNodes = async () => {
    setLoading(true);
    try {
      const response = await frpsNodesAPI.getNodes();
      const allNodes = response.data || [];
      // 只显示激活且在线的节点
      const activeNodes = allNodes.filter((node: FrpsNode) => 
        node.is_active && node.status === 'online'
      );
      setNodes(activeNodes);
    } catch (error) {
      message.error('获取节点列表失败');
      console.error('Error fetching nodes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (nodeId: number) => {
    const selectedNode = nodes.find(node => node.id === nodeId);
    if (selectedNode && onChange) {
      onChange(nodeId, selectedNode);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return '#52c41a';
      case 'offline':
        return '#ff4d4f';
      case 'error':
        return '#fa8c16';
      default:
        return '#d9d9d9';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'error':
        return '错误';
      default:
        return '未知';
    }
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      style={{ minWidth: 200, ...style }}
      disabled={disabled}
      allowClear={allowClear}
      loading={loading}
      notFoundContent={loading ? <Spin size="small" /> : '暂无可用节点'}
      suffixIcon={<DatabaseOutlined />}
      onDropdownVisibleChange={(open) => {
        if (open) {
          fetchNodes(); // 下拉时刷新节点列表
        }
      }}
    >
      {nodes.map((node) => (
        <Option key={node.id} value={node.id}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <div style={{ fontWeight: 'bold' }}>{node.name}</div>
              {node.description && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                  {node.description}
                </div>
              )}
              <div style={{ fontSize: '12px', color: '#999', marginTop: '2px' }}>
                {node.host}:{node.admin_port}
              </div>
            </div>
            {showStatus && (
              <div style={{ marginLeft: '8px' }}>
                <span
                  style={{
                    display: 'inline-block',
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: getStatusColor(node.status),
                    marginRight: '4px',
                  }}
                />
                <span style={{ fontSize: '12px', color: '#666' }}>
                  {getStatusText(node.status)}
                </span>
              </div>
            )}
          </div>
        </Option>
      ))}
    </Select>
  );
};

export default NodeSelector;
