package database

import (
	"frp-panel/pkg/model"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

func Init(dsn string) error {
	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return err
	}

	// 自动迁移所有模型
	return DB.AutoMigrate(
		&model.User{},
		&model.Service{},
		&model.TrafficUsage{},
		&model.Proxy{},
		&model.RBACAudit{},
		&model.ServiceSubscription{},
		&model.FrpsNode{},
		&model.FrpsNodeStats{},
		&model.Domain{},
	)
}
