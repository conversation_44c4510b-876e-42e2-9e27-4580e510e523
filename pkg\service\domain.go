package service

import (
	"errors"
	"fmt"

	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
)

type DomainService struct {
	baseDomain string
}

func NewDomainService(baseDomain string) *DomainService {
	return &DomainService{
		baseDomain: baseDomain,
	}
}

// GetAllDomains returns all domains with user and node information
func (s *DomainService) GetAllDomains() ([]model.Domain, error) {
	var domains []model.Domain
	result := database.DB.Preload("User").Preload("Node").Find(&domains)
	return domains, result.Error
}

// GetDomainByID returns a domain by ID
func (s *DomainService) GetDomainByID(id uint) (*model.Domain, error) {
	var domain model.Domain
	result := database.DB.Preload("User").Preload("Node").First(&domain, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &domain, nil
}

// GetDomainByName returns a domain by domain name
func (s *DomainService) GetDomainByName(domainName string) (*model.Domain, error) {
	var domain model.Domain
	result := database.DB.Preload("User").Preload("Node").Where("domain = ?", domainName).First(&domain)
	if result.Error != nil {
		return nil, result.Error
	}
	return &domain, nil
}

// GetUserByDomain returns user information by domain name
func (s *DomainService) GetUserByDomain(domainName string) (*model.User, error) {
	domain, err := s.GetDomainByName(domainName)
	if err != nil {
		return nil, err
	}

	if !domain.IsActive || domain.Status != model.DomainStatusActive {
		return nil, errors.New("domain is not active")
	}

	return &domain.User, nil
}

// GetDomainsByUserID returns all domains for a specific user
func (s *DomainService) GetDomainsByUserID(userID uint) ([]model.Domain, error) {
	var domains []model.Domain
	result := database.DB.Preload("User").Preload("Node").Where("user_id = ?", userID).Find(&domains)
	return domains, result.Error
}

// GetDomainsByNodeID returns all domains for a specific node
func (s *DomainService) GetDomainsByNodeID(nodeID uint) ([]model.Domain, error) {
	var domains []model.Domain
	result := database.DB.Preload("User").Preload("Node").Where("node_id = ?", nodeID).Find(&domains)
	return domains, result.Error
}

// CreateDomain creates a new domain
func (s *DomainService) CreateDomain(userID, nodeID uint, description string) (*model.Domain, error) {
	// Get user and node information
	var user model.User
	if err := database.DB.First(&user, userID).Error; err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	var node model.FrpsNode
	if err := database.DB.First(&node, nodeID).Error; err != nil {
		return nil, fmt.Errorf("node not found: %w", err)
	}

	// Generate domain name
	domainName := fmt.Sprintf("%s.%s.%s", user.Username, node.Name, s.baseDomain)

	// Check if domain already exists
	var existingDomain model.Domain
	if err := database.DB.Where("domain = ?", domainName).First(&existingDomain).Error; err == nil {
		return nil, errors.New("domain already exists")
	}

	// Create new domain
	domain := model.Domain{
		Domain:      domainName,
		UserID:      userID,
		NodeID:      nodeID,
		Status:      model.DomainStatusPending,
		Description: description,
		IsActive:    true,
	}

	if err := database.DB.Create(&domain).Error; err != nil {
		return nil, err
	}

	// Load relationships
	if err := database.DB.Preload("User").Preload("Node").First(&domain, domain.ID).Error; err != nil {
		return nil, err
	}

	return &domain, nil
}

// UpdateDomain updates an existing domain
func (s *DomainService) UpdateDomain(id uint, updates map[string]interface{}) (*model.Domain, error) {
	var domain model.Domain
	if err := database.DB.First(&domain, id).Error; err != nil {
		return nil, err
	}

	// Validate updates
	if status, ok := updates["status"]; ok {
		if statusStr, ok := status.(string); ok {
			switch model.DomainStatus(statusStr) {
			case model.DomainStatusActive, model.DomainStatusInactive, model.DomainStatusPending, model.DomainStatusError:
				// Valid status
			default:
				return nil, errors.New("invalid domain status")
			}
		}
	}

	if err := database.DB.Model(&domain).Updates(updates).Error; err != nil {
		return nil, err
	}

	// Load relationships
	if err := database.DB.Preload("User").Preload("Node").First(&domain, id).Error; err != nil {
		return nil, err
	}

	return &domain, nil
}

// DeleteDomain deletes a domain
func (s *DomainService) DeleteDomain(id uint) error {
	return database.DB.Delete(&model.Domain{}, id).Error
}

// ActivateDomain activates a domain
func (s *DomainService) ActivateDomain(id uint) (*model.Domain, error) {
	return s.UpdateDomain(id, map[string]interface{}{
		"status":    model.DomainStatusActive,
		"is_active": true,
	})
}

// DeactivateDomain deactivates a domain
func (s *DomainService) DeactivateDomain(id uint) (*model.Domain, error) {
	return s.UpdateDomain(id, map[string]interface{}{
		"status":    model.DomainStatusInactive,
		"is_active": false,
	})
}

// ValidateDomainFormat validates if a domain follows the expected format
func (s *DomainService) ValidateDomainFormat(domainName string) (username, nodeName string, err error) {
	return model.ParseDomain(domainName, s.baseDomain)
}

// GetDomainStats returns statistics about domains
func (s *DomainService) GetDomainStats() (map[string]interface{}, error) {
	var stats struct {
		Total    int64 `json:"total"`
		Active   int64 `json:"active"`
		Inactive int64 `json:"inactive"`
		Pending  int64 `json:"pending"`
		Error    int64 `json:"error"`
	}

	// Count total domains
	database.DB.Model(&model.Domain{}).Count(&stats.Total)

	// Count by status
	database.DB.Model(&model.Domain{}).Where("status = ?", model.DomainStatusActive).Count(&stats.Active)
	database.DB.Model(&model.Domain{}).Where("status = ?", model.DomainStatusInactive).Count(&stats.Inactive)
	database.DB.Model(&model.Domain{}).Where("status = ?", model.DomainStatusPending).Count(&stats.Pending)
	database.DB.Model(&model.Domain{}).Where("status = ?", model.DomainStatusError).Count(&stats.Error)

	return map[string]interface{}{
		"total_domains":    stats.Total,
		"active_domains":   stats.Active,
		"inactive_domains": stats.Inactive,
		"pending_domains":  stats.Pending,
		"error_domains":    stats.Error,
	}, nil
}

// CheckDomainConflict checks if a domain would conflict with existing domains
func (s *DomainService) CheckDomainConflict(userID, nodeID uint) (bool, string, error) {
	// Get user and node
	var user model.User
	if err := database.DB.First(&user, userID).Error; err != nil {
		return false, "", fmt.Errorf("user not found: %w", err)
	}

	var node model.FrpsNode
	if err := database.DB.First(&node, nodeID).Error; err != nil {
		return false, "", fmt.Errorf("node not found: %w", err)
	}

	// Generate expected domain
	expectedDomain := fmt.Sprintf("%s.%s.%s", user.Username, node.Name, s.baseDomain)

	// Check if domain exists
	var existingDomain model.Domain
	err := database.DB.Where("domain = ?", expectedDomain).First(&existingDomain).Error
	if err == nil {
		return true, expectedDomain, nil // Domain exists
	}

	return false, expectedDomain, nil // Domain doesn't exist
}
