import axios from 'axios';

// 创建axios实例
const api = axios.create({
  // 使用相对路径，开发阶段通过 Vite 代理到 Go 后端；生产同域部署
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 10000,
});

// 获取认证token
const getAuthToken = (): string => {
  return localStorage.getItem('admin_token') || getCookie('admin_token') || '';
};

// RBAC 管理接口
export const rbacAPI = {
  // 策略管理
  listPolicies: () => api.get('/admin/rbac/policies'),
  addPolicy: (data: { sub: string; obj: string; act: string }) => api.post('/admin/rbac/policies', data),
  removePolicy: (data: { sub: string; obj: string; act: string }) => api.delete('/admin/rbac/policies', { data }),
  // 角色分配
  addRoleForUser: (data: { user: string; role: string }) => api.post('/admin/rbac/roles', data),
  removeRoleForUser: (data: { user: string; role: string }) => api.delete('/admin/rbac/roles', { data }),
  getRolesForUser: (user: string) => api.get(`/admin/rbac/roles/${encodeURIComponent(user)}`),
  // 重新加载策略
  reload: () => api.post('/admin/rbac/reload'),
};

// 获取cookie值
const getCookie = (name: string): string => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || '';
  return '';
};

// 请求拦截器 - 添加认证头
api.interceptors.request.use(
  (config) => {
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('admin_token');
      document.cookie = 'admin_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      window.location.href = '/login';
    } else {
      // 显示错误信息
      const errorMessage = error.response?.data?.message || error.message || '请求失败';
      console.error(errorMessage)
      // message.error(errorMessage);
    }
    return Promise.reject(error);
  }
);

// API接口定义
export const dashboardAPI = {
  // 获取仪表板数据
  getDashboard: () => api.get('/admin/dashboard'),
};

// 管理员接口
export const adminAPI = {
  getProfile: () => api.get('/api/profile'),
};

// 公共认证接口
export const authAPI = {
  // 用户注册
  register: (data: { username: string; email: string; password: string }) => api.post('/api/register', data),
  // 用户登录（email + password）
  login: (data: { email: string; password: string }) => api.post('/api/login', data),
};

// 普通用户接口（使用用户 token）
export const userAPI = {
  getProfile: () => api.get('/api/profile', {
    headers: { Authorization: localStorage.getItem('token') || '' },
  }),
};

export const usersAPI = {
  // 获取用户列表
  getUsers: () => api.get('/admin/users'),
  // 创建用户
  createUser: (userData: any) => api.post('/admin/users', userData),
  // 更新用户
  updateUser: (id: number, userData: any) => api.put(`/admin/users/${id}`, userData),
  // 删除用户
  deleteUser: (id: number) => api.delete(`/admin/users/${id}`),
  // 重置用户当月流量
  resetTraffic: (id: number) => api.post(`/admin/users/${id}/reset-traffic`),
  // 关闭用户代理（标记为非活跃；可扩展为调用 frps admin 关闭）
  closeProxies: (id: number) => api.post(`/admin/users/${id}/close-proxies`),
};

export const servicesAPI = {
  // 获取服务列表
  getServices: () => api.get('/admin/services'),
  // 获取我的订阅
  getSubscriptions: () => api.get('/api/subscriptions'),
  // 购买服务
  purchaseService: (serviceId: number) => api.post('/api/subscriptions/purchase', { service_id: serviceId }),
  // 创建服务
  createService: (serviceData: any) => api.post('/admin/services', serviceData),
  // 更新服务
  updateService: (id: number, serviceData: any) => api.put(`/admin/services/${id}`, serviceData),
  // 删除服务
  deleteService: (id: number) => api.delete(`/admin/services/${id}`),
};

export const trafficAPI = {
  // 获取流量统计
  getTrafficStats: () => api.get('/admin/traffic/stats'),
  // 获取每日流量数据
  getDailyTraffic: (days: number = 30) => api.get(`/admin/traffic?days=${days}`),
  // 清理旧数据
  cleanupOldData: () => api.post('/admin/traffic/cleanup'),
};

// FRPS API接口定义 - 通过 Go 后端代理转发到 frps
export const frpsAPI = {
  // 获取服务器信息 - 支持节点ID参数
  getServerInfo: (nodeId?: number) => {
    if (nodeId) {
      return api.get(`/admin/nodes/${nodeId}/frps/serverinfo`);
    }
    return api.get('/admin/frps/serverinfo'); // 兼容旧版本，使用默认节点
  },

  // 获取代理信息 - 支持节点ID参数
  getProxies: (type: string, nodeId?: number) => {
    if (nodeId) {
      return api.get(`/admin/nodes/${nodeId}/frps/proxy/${type}`);
    }
    return api.get(`/admin/frps/proxy/${type}`); // 兼容旧版本，使用默认节点
  },

  // 清理离线代理 - 支持节点ID参数
  clearOfflineProxies: (nodeId?: number) => {
    if (nodeId) {
      return api.delete(`/admin/nodes/${nodeId}/frps/proxies?status=offline`);
    }
    return api.delete('/admin/frps/proxies?status=offline'); // 兼容旧版本，使用默认节点
  },

  // 获取流量信息 - 支持节点ID参数
  getTrafficInfo: (proxyName: string, nodeId?: number) => {
    if (nodeId) {
      return api.get(`/admin/nodes/${nodeId}/frps/traffic/${proxyName}`);
    }
    return api.get(`/admin/frps/traffic/${proxyName}`); // 兼容旧版本，使用默认节点
  },
};

// FRPS 节点管理 API接口定义
export const frpsNodesAPI = {
  // 获取所有节点
  getNodes: () => api.get('/admin/nodes'),

  // 创建节点
  createNode: (nodeData: any) => api.post('/admin/nodes', nodeData),

  // 获取单个节点
  getNode: (id: number) => api.get(`/admin/nodes/${id}`),

  // 更新节点
  updateNode: (id: number, nodeData: any) => api.put(`/admin/nodes/${id}`, nodeData),

  // 删除节点
  deleteNode: (id: number) => api.delete(`/admin/nodes/${id}`),

  // 检查节点状态
  checkNodeStatus: (id: number) => api.post(`/admin/nodes/${id}/check`),

  // 切换节点状态
  toggleNodeStatus: (id: number) => api.post(`/admin/nodes/${id}/toggle`),

  // 获取节点统计
  getNodeStats: (id: number, days: number = 30) => api.get(`/admin/nodes/${id}/stats?days=${days}`),

  // 检查所有节点状态
  checkAllNodes: () => api.post('/admin/nodes/check-all'),

  // 获取仪表板统计
  getDashboardStats: () => api.get('/admin/nodes/dashboard'),
};

export default api;
