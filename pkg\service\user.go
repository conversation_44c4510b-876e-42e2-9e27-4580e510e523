package service

import (
	"errors"
	"time"

	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
)

type UserService struct{}

func NewUserService() *UserService {
	return &UserService{}
}

// GetAllUsers returns all users (admin only)
func (s *UserService) GetAllUsers() ([]model.User, error) {
	var users []model.User
	result := database.DB.Preload("Service").Find(&users)
	return users, result.Error
}

// GetUserByID returns a user by ID
func (s *UserService) GetUserByID(id uint) (*model.User, error) {
	var user model.User
	result := database.DB.Preload("Service").Preload("TrafficUsage").First(&user, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &user, nil
}

// UpdateUser updates user information
func (s *UserService) UpdateUser(user *model.User) error {
	return database.DB.Save(user).Error
}

// DeleteUser soft deletes a user
func (s *UserService) DeleteUser(id uint) error {
	return database.DB.Delete(&model.User{}, id).Error
}

// PurchaseService assigns a service to a user
func (s *UserService) PurchaseService(userID, serviceID uint) error {
	var user model.User
	if err := database.DB.First(&user, userID).Error; err != nil {
		return err
	}

	var service model.Service
	if err := database.DB.First(&service, serviceID).Error; err != nil {
		return err
	}

	if !service.Active {
		return errors.New("service is not active")
	}

	user.ServiceID = &serviceID
	return database.DB.Save(&user).Error
}

// CancelService removes service from user
func (s *UserService) CancelService(userID uint) error {
	var user model.User
	if err := database.DB.First(&user, userID).Error; err != nil {
		return err
	}

	user.ServiceID = nil
	return database.DB.Save(&user).Error
}

// GetUserStats returns user statistics
func (s *UserService) GetUserStats(userID uint) (map[string]interface{}, error) {
	var user model.User
	result := database.DB.Preload("Service").Preload("TrafficUsage").First(&user, userID)
	if result.Error != nil {
		return nil, result.Error
	}

	stats := map[string]interface{}{
		"user_id":            user.ID,
		"username":           user.Username,
		"email":              user.Email,
		"service":            user.Service,
		"monthly_traffic_mb": user.GetMonthlyTrafficMB(),
		"traffic_exceeded":   user.IsTrafficExceeded(),
		"created_at":         user.CreatedAt,
	}

	if user.Service != nil {
		stats["traffic_limit_mb"] = user.Service.TrafficMB
		stats["max_tunnels"] = user.Service.MaxTunnels
		stats["traffic_usage_percent"] = float64(user.GetMonthlyTrafficMB()) / float64(user.Service.TrafficMB) * 100
	}

	return stats, nil
}

// GetDashboardStats returns overall dashboard statistics
func (s *UserService) GetDashboardStats() (map[string]interface{}, error) {
	var totalUsers int64
	var activeUsers int64
	var totalServices int64

	database.DB.Model(&model.User{}).Count(&totalUsers)
	database.DB.Model(&model.User{}).Where("status = 1").Count(&activeUsers)
	database.DB.Model(&model.Service{}).Where("active = true").Count(&totalServices)

	// Get traffic usage for current month
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	var monthlyTraffic int64
	database.DB.Model(&model.TrafficUsage{}).
		Where("date >= ?", startOfMonth).
		Select("COALESCE(SUM(bytes), 0)").
		Scan(&monthlyTraffic)

	stats := map[string]interface{}{
		"total_users":        totalUsers,
		"active_users":       activeUsers,
		"total_services":     totalServices,
		"monthly_traffic_gb": float64(monthlyTraffic) / (1024 * 1024 * 1024),
		"last_updated":       time.Now(),
	}

	return stats, nil
}
