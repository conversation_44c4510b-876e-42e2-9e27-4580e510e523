import { frpsAPI } from "@/utils/api";
import { UDPProxy } from "@/utils/proxy";
import { message } from "antd";
import React, { useEffect, useState } from "react";
import ProxyTable from "../components/ProxyTable";

interface ProxiesUDPProps {
  nodeId?: number;
}

const ProxiesUDP: React.FC<ProxiesUDPProps> = ({ nodeId }) => {
  const [loading, setLoading] = useState(false);
  const [proxies, setProxies] = useState<any[]>([]);

  useEffect(() => {
    if (nodeId) {
      fetchData();
    } else {
      setProxies([]);
    }
  }, [nodeId]);

  const fetchData = async () => {
    if (!nodeId) return;

    setLoading(true);
    try {
      const proxyResponse = await frpsAPI.getProxies("udp", nodeId);
      const proxyData = proxyResponse.data;

      if (proxyData?.proxies) {
        const udpProxies = proxyData.proxies.map(
          (proxy: any) => new UDPProxy(proxy)
        );
        setProxies(udpProxies);
      } else {
        setProxies([]);
      }
    } catch (error) {
      message.error("获取UDP代理数据失败");
      console.error("Error fetching UDP proxies:", error);
      setProxies([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <ProxyTable
        proxies={proxies}
        proxyType="udp"
        loading={loading}
        onRefresh={fetchData}
      />
    </div>
  );
};

export default ProxiesUDP;
