import { frpsAPI } from "@/utils/api";
import { STCPProxy } from "@/utils/proxy";
import { message } from "antd";
import React, { useEffect, useState } from "react";
import ProxyTable from "../components/ProxyTable";

interface ProxiesSTCPProps {
  nodeId?: number;
}

const ProxiesSTCP: React.FC<ProxiesSTCPProps> = ({ nodeId }) => {
  const [loading, setLoading] = useState(false);
  const [proxies, setProxies] = useState<any[]>([]);

  useEffect(() => {
    if (nodeId) {
      fetchData();
    } else {
      setProxies([]);
    }
  }, [nodeId]);

  const fetchData = async () => {
    if (!nodeId) return;

    setLoading(true);
    try {
      const proxyResponse = await frpsAPI.getProxies("stcp", nodeId);
      const proxyData = proxyResponse.data;

      if (proxyData?.proxies) {
        const stcpProxies = proxyData.proxies.map(
          (proxy: any) => new STCPProxy(proxy)
        );
        setProxies(stcpProxies);
      } else {
        setProxies([]);
      }
    } catch (error) {
      message.error("获取STCP代理数据失败");
      console.error("Error fetching STCP proxies:", error);
      setProxies([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <ProxyTable
        proxies={proxies}
        proxyType="stcp"
        loading={loading}
        onRefresh={fetchData}
      />
    </div>
  );
};

export default ProxiesSTCP;
