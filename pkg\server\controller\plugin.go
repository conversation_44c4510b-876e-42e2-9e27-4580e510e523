package controller

import (
	"encoding/json"
	"github.com/fatedier/frp/pkg/msg"
	"github.com/fatedier/frp/pkg/plugin/server"
	"io"
	"log"
	"net/http"

	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
	"frp-panel/pkg/service"

	"github.com/gin-gonic/gin"
)

// FRP HTTP Plugin expects handlers for ops like Login, NewProxy, NewWorkConn, Ping, etc.
// We use metas.token and metas.username to authenticate and check quota.

type PluginController struct {
	authService    *service.AuthService
	trafficService *service.TrafficService
	proxyService   *service.ProxyService
}

func NewPluginController(auth *service.AuthService, traffic *service.TrafficService) *PluginController {
	return &PluginController{
		authService:    auth,
		trafficService: traffic,
		proxyService:   service.NewProxyService(),
	}
}

func (pc *PluginController) Register(engine *gin.Engine) {
	grp := engine.Group("/plugin")
	{
		// FRP HTTP plugin will POST to the configured path with op/version as query parameters
		// e.g. /plugin?op=Login&version=v1.0.0
		grp.POST("", pc.handleEntry)

		// Keep explicit endpoints for compatibility if someone configured path with suffixes
		grp.POST("/login", pc.handleLogin)
		grp.POST("/newProxy", pc.handleNewProxy)
		grp.POST("/closeProxy", pc.handleCloseProxy)
		grp.POST("/newWorkConn", pc.handleNewWorkConn)
		grp.POST("/ping", pc.handlePing)
		grp.POST("/newUserConn", pc.handleNewUserConn)
	}
}

// FRP HTTP plugin request envelope
type frpPluginEnvelope struct {
	Version string           `json:"version"`
	Op      string           `json:"op"`
	Content frpPluginContent `json:"content"`
}

// Content carries fields specific to each op; we only extract what we need
type frpPluginContent struct {
	// Common fields
	Version string            `json:"version"`
	Metas   map[string]string `json:"metas,omitempty"`

	// User information (for Login, NewWorkConn, Ping, NewUserConn)
	User *frpUser `json:"user,omitempty"`

	// Proxy information (for NewProxy, CloseProxy)
	ProxyName string `json:"proxy_name,omitempty"`
	ProxyType string `json:"proxy_type,omitempty"`

	// Work connection information (for NewWorkConn)
	RunID     string `json:"run_id,omitempty"`
	Timestamp int64  `json:"timestamp,omitempty"`

	// User connection information (for NewUserConn)
	RemoteAddr string `json:"remote_addr,omitempty"`
}

type frpUser struct {
	User  string            `json:"user"`
	Metas map[string]string `json:"metas"`
	RunID string            `json:"run_id"`
}

// handleEntry dispatches by op query parameter as per FRP HTTP plugin spec
func (pc *PluginController) handleEntry(ctx *gin.Context) {
	op := ctx.Query("op")
	switch op {
	case "Login":
		pc.handleLogin(ctx)
	case "NewProxy":
		pc.handleNewProxy(ctx)
	case "CloseProxy":
		pc.handleCloseProxy(ctx)
	case "NewWorkConn":
		pc.handleNewWorkConn(ctx)
	case "Ping":
		pc.handlePing(ctx)
	case "NewUserConn":
		pc.handleNewUserConn(ctx)
	default:
		ctx.JSON(http.StatusBadRequest, &server.Response{
			Reject:       true,
			RejectReason: "unknown op: " + op,
		})
	}
}

// authenticate extracts user credentials and validates them
func (pc *PluginController) authenticate(content *frpPluginContent) (*server.Response, *service.AuthenticatedUser) {
	// Extract token and username from metas (old format) or user field (new format)
	var token, username string

	// Try to get from metas first (legacy format)
	if content.Metas != nil {
		token = content.Metas["token"]
		username = content.Metas["username"]
	}

	// Try to get from user field (new format)
	if (token == "" || username == "") && content.User != nil {
		if content.User.Metas != nil {
			if token == "" {
				token = content.User.Metas["token"]
			}
			if username == "" {
				username = content.User.Metas["username"]
			}
		}
		// Fallback to user field if metas is not available
		if username == "" {
			username = content.User.User
		}
		// Fallback to user.run_id if token is not available
		if token == "" {
			token = content.User.RunID
		}
	}

	// Fallback to top-level run_id if still not found
	if token == "" {
		token = content.RunID
	}

	// For debugging - log what we extracted
	log.Printf("Extracted token: %s, username: %s from content: %+v", token, username, content)

	// If we still don't have a token but have a run_id, try to get user by run_id
	if token == "" && content.RunID != "" {
		log.Printf("Attempting to find user by run_id: %s", content.RunID)
		userModel, err := pc.authService.GetUserByRunID(content.RunID)
		if err != nil {
			log.Printf("Failed to find user by run_id: %s, error: %v", content.RunID, err)
			return &server.Response{
				Reject:       true,
				RejectReason: "invalid run_id",
			}, nil
		}

		// Set the token to the user's actual token for consistency
		token = userModel.Token
		username = userModel.Username

		log.Printf("Found user by run_id: %s, username: %s", content.RunID, username)
	} else if token == "" || username == "" {
		return &server.Response{
			Reject:       true,
			RejectReason: "missing token or username",
		}, nil
	}

	userModel, err := pc.authService.GetUserByToken(token)
	if err != nil {
		log.Printf("Failed to find user by token: %s, error: %v", token, err)
		return &server.Response{
			Reject:       true,
			RejectReason: "invalid token",
		}, nil
	}

	if userModel.Username != username {
		log.Printf("Username mismatch: expected %s, got %s", userModel.Username, username)
		return &server.Response{
			Reject:       true,
			RejectReason: "username mismatch",
		}, nil
	}

	if userModel.Status != 1 {
		log.Printf("User inactive: %s", userModel.Username)
		return &server.Response{
			Reject:       true,
			RejectReason: "user inactive",
		}, nil
	}

	// Check if user has exceeded traffic limit
	if userModel.Service != nil {
		exceeded, err := pc.trafficService.CheckTrafficLimit(userModel.ID)
		if err != nil {
			log.Printf("Failed to check traffic limit for user %s: %v", userModel.Username, err)
			return &server.Response{
				Reject:       true,
				RejectReason: "server error",
			}, nil
		}
		if exceeded {
			return &server.Response{
				Reject:       true,
				RejectReason: "traffic limit exceeded",
			}, nil
		}
	}

	// Apply service plan limits or sensible defaults
	maxTunnels := 5
	if userModel.Service != nil && userModel.Service.MaxTunnels > 0 {
		maxTunnels = userModel.Service.MaxTunnels
	}

	return nil, &service.AuthenticatedUser{
		ID:         userModel.ID,
		Username:   userModel.Username,
		MaxTunnels: maxTunnels,
	}
}

func (pc *PluginController) handleLogin(ctx *gin.Context) {
	var env frpPluginEnvelope
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	log.Printf("frp plugin login request: %s", body)

	if err = json.Unmarshal(body, &env); err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	// Extract run_id from the login request
	var runID string
	if env.Content.User != nil && env.Content.User.RunID != "" {
		runID = env.Content.User.RunID
	} else if env.Content.RunID != "" {
		runID = env.Content.RunID
	}

	log.Printf("Extracted run_id from login: %s", runID)

	rej, authUser := pc.authenticate(&env.Content)
	if rej != nil {
		ctx.JSON(http.StatusOK, rej)
		return
	}
	if authUser == nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "authentication failed",
		})
		return
	}

	// Update the user's run_id in the database
	if runID != "" {
		var user model.User
		result := database.DB.Where("id = ?", authUser.ID).First(&user)
		if result.Error == nil {
			user.RunID = runID
			database.DB.Save(&user)
			log.Printf("Updated user %s with run_id: %s", user.Username, runID)
		}
	}

	// Return the modified login content to frp
	// This allows us to modify the user information that frp will use
	modifiedContent := msg.Login{
		Version:    env.Content.Version,
		User:       authUser.Username,
		RunID:      runID,
		Metas:      env.Content.Metas,
		ClientSpec: msg.ClientSpec{AlwaysAuthPass: true}, // todo frps changed
		PoolCount:  0,
	}

	ctx.JSON(http.StatusOK, &server.Response{
		Reject:   false,
		Unchange: false,
		Content:  modifiedContent,
	})
}

func (pc *PluginController) handleNewProxy(ctx *gin.Context) {
	var env frpPluginEnvelope
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	log.Printf("frp plugin newProxy request: %s", body)

	if err := json.Unmarshal(body, &env); err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	rej, authUser := pc.authenticate(&env.Content)
	if rej != nil {
		ctx.JSON(http.StatusOK, rej)
		return
	}
	if authUser == nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "authentication failed",
		})
		return
	}

	// Check traffic limit again for NewProxy (double-check)
	exceeded, err := pc.trafficService.CheckTrafficLimit(authUser.ID)
	if err != nil {
		log.Printf("Failed to check traffic limit for user %s in NewProxy: %v", authUser.Username, err)
	} else if exceeded {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "traffic limit exceeded",
		})
		return
	}

	// Enforce MaxTunnels
	cnt, err := pc.proxyService.CountActiveByUser(authUser.ID)
	if err != nil {
		log.Printf("Failed to count active proxies for user %s: %v", authUser.Username, err)
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "server error",
		})
		return
	}

	if cnt >= authUser.MaxTunnels {
		log.Printf("User %s exceeded max tunnels: %d/%d", authUser.Username, cnt, authUser.MaxTunnels)
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "max tunnels exceeded",
		})
		return
	}

	// Ensure proxy record exists/active
	if err := pc.proxyService.EnsureProxy(authUser.ID, env.Content.ProxyName, env.Content.ProxyType); err != nil {
		log.Printf("Failed to ensure proxy for user %s: %v", authUser.Username, err)
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "server error",
		})
		return
	}

	log.Printf("NewProxy approved for user %s: %s (%s)", authUser.Username, env.Content.ProxyName, env.Content.ProxyType)

	// Return modified proxy content to frp
	// We can modify proxy settings here if needed
	modifiedContent := map[string]any{
		"user": map[string]any{
			"user":   authUser.Username,
			"metas":  env.Content.Metas,
			"run_id": env.Content.RunID,
		},
		"proxy_name": env.Content.ProxyName,
		"proxy_type": env.Content.ProxyType,
		// Add any other proxy configuration modifications here
	}

	ctx.JSON(http.StatusOK, &server.Response{
		Reject:   false,
		Unchange: false, // Allow frp to use our modified content
		Content:  modifiedContent,
	})
}

func (pc *PluginController) handleCloseProxy(ctx *gin.Context) {
	var env frpPluginEnvelope
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	log.Printf("frp plugin closeProxy request: %s", body)

	if err := json.Unmarshal(body, &env); err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	rej, authUser := pc.authenticate(&env.Content)
	if rej != nil {
		ctx.JSON(http.StatusOK, rej)
		return
	}
	if authUser == nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "authentication failed",
		})
		return
	}

	log.Printf("CloseProxy for user %s: %s (%s)", authUser.Username, env.Content.ProxyName, env.Content.ProxyType)

	// For CloseProxy, we just acknowledge the request
	ctx.JSON(http.StatusOK, &server.Response{
		Reject: false,
	})
}

func (pc *PluginController) handleNewWorkConn(ctx *gin.Context) {
	var env frpPluginEnvelope
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	log.Printf("frp plugin newWorkConn request: %s", body)

	if err := json.Unmarshal(body, &env); err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	// Log the content structure for debugging
	log.Printf("NewWorkConn content: %+v", env.Content)

	rej, authUser := pc.authenticate(&env.Content)
	if rej != nil {
		log.Printf("NewWorkConn authentication failed: %+v", rej)
		ctx.JSON(http.StatusOK, rej)
		return
	}
	if authUser == nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "authentication failed",
		})
		return
	}

	log.Printf("NewWorkConn authenticated user: %+v", authUser)

	// Return modified work connection content to frp
	modifiedContent := map[string]any{
		"user": map[string]any{
			"user":   authUser.Username,
			"metas":  env.Content.Metas,
			"run_id": env.Content.RunID,
		},
		"run_id":    env.Content.RunID,
		"timestamp": env.Content.Timestamp,
	}

	ctx.JSON(http.StatusOK, &server.Response{
		Reject:   false,
		Unchange: false, // Allow frp to use our modified content
		Content:  modifiedContent,
	})
}

func (pc *PluginController) handlePing(ctx *gin.Context) {
	var env frpPluginEnvelope
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	log.Printf("frp plugin ping request: %s", body)

	if err := json.Unmarshal(body, &env); err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	rej, _ := pc.authenticate(&env.Content)
	if rej != nil {
		ctx.JSON(http.StatusOK, rej)
		return
	}

	ctx.JSON(http.StatusOK, &server.Response{
		Reject: false,
	})
}

func (pc *PluginController) handleNewUserConn(ctx *gin.Context) {
	var env frpPluginEnvelope
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	log.Printf("frp plugin newUserConn request: %s", body)

	if err := json.Unmarshal(body, &env); err != nil {
		ctx.JSON(http.StatusOK, &server.Response{
			Reject:       true,
			RejectReason: "bad request",
		})
		return
	}

	rej, _ := pc.authenticate(&env.Content)
	if rej != nil {
		ctx.JSON(http.StatusOK, rej)
		return
	}

	// For NewUserConn, we just acknowledge the request
	ctx.JSON(http.StatusOK, &server.Response{
		Reject: false,
	})
}
