import React, { useState, useEffect } from "react";
import { Card, Tabs } from "antd";
import ProxiesHTTP from "../pages/Frps/components/ProxiesHTTP";
import ProxiesHTTPS from "../pages/Frps/components/ProxiesHTTPS";
import ProxiesTCP from "../pages/Frps/components/ProxiesTCP";
import ProxiesUDP from "../pages/Frps/components/ProxiesUDP";
import ProxiesSTCP from "../pages/Frps/components/ProxiesSTCP";
import ProxiesSUDP from "../pages/Frps/components/ProxiesSUDP";
import ProxiesTCPMux from "../pages/Frps/components/ProxiesTCPMux";

interface FrpsNode {
  id: number;
  name: string;
  description: string;
  host: string;
  port: number;
  admin_port: number;
  status: 'online' | 'offline' | 'error' | 'unknown';
  is_active: boolean;
}

interface NodeProxiesProps {
  nodeId: number;
  node: FrpsNode;
  refreshKey?: number;
}

const NodeProxies: React.FC<NodeProxiesProps> = ({ nodeId, node, refreshKey }) => {
  const [activeTab, setActiveTab] = useState('tcp');

  // 当refreshKey变化时，可以触发子组件刷新
  useEffect(() => {
    // 这里可以添加刷新逻辑，如果需要的话
  }, [refreshKey]);

  const tabItems = [
    {
      key: "tcp",
      label: "TCP",
      children: <ProxiesTCP nodeId={nodeId} key={`tcp-${refreshKey}`} />,
    },
    {
      key: "udp",
      label: "UDP",
      children: <ProxiesUDP nodeId={nodeId} key={`udp-${refreshKey}`} />,
    },
    {
      key: "http",
      label: "HTTP",
      children: <ProxiesHTTP nodeId={nodeId} key={`http-${refreshKey}`} />,
    },
    {
      key: "https",
      label: "HTTPS",
      children: <ProxiesHTTPS nodeId={nodeId} key={`https-${refreshKey}`} />,
    },
    {
      key: "tcpmux",
      label: "TCPMux",
      children: <ProxiesTCPMux nodeId={nodeId} key={`tcpmux-${refreshKey}`} />,
    },
    {
      key: "stcp",
      label: "STCP",
      children: <ProxiesSTCP nodeId={nodeId} key={`stcp-${refreshKey}`} />,
    },
    {
      key: "sudp",
      label: "SUDP",
      children: <ProxiesSUDP nodeId={nodeId} key={`sudp-${refreshKey}`} />,
    },
  ];

  return (
    <div>
      <Tabs 
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems} 
        defaultActiveKey="tcp" 
      />
    </div>
  );
};

export default NodeProxies;
