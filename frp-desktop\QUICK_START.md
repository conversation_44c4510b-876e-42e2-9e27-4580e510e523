# FRP Desktop Client - 快速启动指南

## 🚀 快速启动

### Windows 用户
1. 双击 `start.bat` 文件
2. 等待依赖安装完成
3. 应用将自动启动

### macOS/Linux 用户
```bash
chmod +x start.sh
./start.sh
```

### 手动启动
```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run electron:dev
```

## 🔧 环境要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+

## 📋 使用步骤

### 1. 启动后端服务
确保后端服务运行在 `http://localhost:7200`

### 2. 登录应用
- 首次使用请注册新账户
- 已有账户直接登录

### 3. 配置 FRPC
- 点击左侧菜单"配置管理"
- 填写服务器信息和隧道配置
- 可使用配置模板快速设置

### 4. 启动服务
- 返回"控制台"页面
- 点击大型"启动 FRPC 服务"按钮
- 观察服务状态和日志

## 🎨 界面特色

- **深色侧边栏**: 专业的桌面应用风格
- **大型控制按钮**: 清晰的服务启停控制
- **实时状态监控**: 连接状态、运行时间、流量统计
- **配置模板**: 快速创建常用配置
- **实时日志**: 彩色日志显示，支持搜索和过滤

## ❗ 常见问题

### 依赖安装失败
```bash
# 清理缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 端口被占用
- 检查端口 3344 是否被占用
- 修改 `vite.config.ts` 中的端口配置

### 无法连接后端
- 确认后端服务运行在 localhost:7200
- 检查防火墙设置
- 查看网络连接状态

## 📦 构建应用

```bash
# 构建所有平台
npm run build:electron

# 构建特定平台
npm run build:electron:win    # Windows
npm run build:electron:mac    # macOS  
npm run build:electron:linux  # Linux
```

构建完成后，安装包位于 `release/` 目录下。

## 🆘 获取帮助

如遇问题，请：
1. 查看控制台错误信息
2. 检查应用日志
3. 参考 `TROUBLESHOOTING.md`
4. 联系技术支持
