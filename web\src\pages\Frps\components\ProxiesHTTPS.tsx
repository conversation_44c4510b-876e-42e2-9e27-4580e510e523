import { frpsAPI } from "@/utils/api";
import { HTTPSProxy } from "@/utils/proxy";
import { message } from "antd";
import React, { useEffect, useState } from "react";
import ProxyTable from "../components/ProxyTable";

interface ProxiesHTTPSProps {
  nodeId?: number;
}

const ProxiesHTTPS: React.FC<ProxiesHTTPSProps> = ({ nodeId }) => {
  const [loading, setLoading] = useState(false);
  const [proxies, setProxies] = useState<any[]>([]);

  useEffect(() => {
    if (nodeId) {
      fetchData();
    } else {
      setProxies([]);
    }
  }, [nodeId]);

  const fetchData = async () => {
    if (!nodeId) return;

    setLoading(true);
    try {
      const serverResponse = await frpsAPI.getServerInfo(nodeId);
      const serverData = serverResponse.data;
      const { vhostHTTPSPort, subdomainHost } = serverData;

      if (!vhostHTTPSPort || vhostHTTPSPort === 0) {
        setProxies([]);
        return;
      }

      const proxyResponse = await frpsAPI.getProxies("https", nodeId);
      const proxyData = proxyResponse.data;

      if (proxyData?.proxies) {
        const httpsProxies = proxyData.proxies.map(
          (proxy: any) => new HTTPSProxy(proxy, vhostHTTPSPort, subdomainHost)
        );
        setProxies(httpsProxies);
      } else {
        setProxies([]);
      }
    } catch (error) {
      message.error("获取HTTPS代理数据失败");
      console.error("Error fetching HTTPS proxies:", error);
      setProxies([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <ProxyTable
        proxies={proxies}
        proxyType="https"
        loading={loading}
        onRefresh={fetchData}
      />
    </div>
  );
};

export default ProxiesHTTPS;
