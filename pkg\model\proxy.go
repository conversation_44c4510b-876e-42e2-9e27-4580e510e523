package model

import "time"

// Proxy represents a frp proxy owned by a user
// We persist mapping so we can enforce MaxTunnels and attribute traffic.

type Proxy struct {
	ID           uint      `gorm:"primarykey" json:"id"`
	UserID       uint      `gorm:"index;not null" json:"user_id"`
	Name         string    `gorm:"index;not null;size:100;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"name"`
	Type         string    `gorm:"size:20" json:"type"` // tcp/udp/http/https/stcp/sudp/xtcp
	Active       bool      `gorm:"default:true" json:"active"`
	LastInBytes  int64     `gorm:"default:0" json:"last_in_bytes"`
	LastOutBytes int64     `gorm:"default:0" json:"last_out_bytes"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}
