import { frpsAPI } from "@/utils/api";
import { HTTPProxy } from "@/utils/proxy";
import { message } from "antd";
import React, { useEffect, useState } from "react";
import ProxyTable from "../components/ProxyTable";

interface ProxiesHTTPProps {
  nodeId?: number;
}

const ProxiesHTTP: React.FC<ProxiesHTTPProps> = ({ nodeId }) => {
  const [loading, setLoading] = useState(false);
  const [proxies, setProxies] = useState<any[]>([]);

  useEffect(() => {
    if (nodeId) {
      fetchData();
    } else {
      setProxies([]);
    }
  }, [nodeId]);

  const fetchData = async () => {
    if (!nodeId) return;

    setLoading(true);
    try {
      // First get server info
      const serverResponse = await frpsAPI.getServerInfo(nodeId);
      const serverData = serverResponse.data;

      const { vhostHTTPPort, subdomainHost } = serverData;

      if (!vhostHTTPPort || vhostHTTPPort === 0) {
        setProxies([]);
        return;
      }

      // Get HTTP proxy list
      const proxyResponse = await frpsAPI.getProxies("http", nodeId);
      const proxyData = proxyResponse.data;

      if (proxyData?.proxies) {
        const httpProxies = proxyData.proxies.map(
          (proxy: any) => new HTTPProxy(proxy, vhostHTTPPort, subdomainHost)
        );
        setProxies(httpProxies);
      } else {
        setProxies([]);
      }
    } catch (error) {
      message.error("获取HTTP代理数据失败");
      console.error("Error fetching HTTP proxies:", error);
      setProxies([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <ProxyTable
        proxies={proxies}
        proxyType="http"
        loading={loading}
        onRefresh={fetchData}
      />
    </div>
  );
};

export default ProxiesHTTP;
