import { frpsAPI } from "@/utils/api";
import { TCPMuxProxy } from "@/utils/proxy";
import { message } from "antd";
import React, { useEffect, useState } from "react";
import ProxyTable from "./ProxyTable";

interface ProxiesTCPMuxProps {
  nodeId?: number;
}

const ProxiesTCPMux: React.FC<ProxiesTCPMuxProps> = ({ nodeId }) => {
  const [loading, setLoading] = useState(false);
  const [proxies, setProxies] = useState<any[]>([]);

  useEffect(() => {
    if (nodeId) {
      fetchData();
    } else {
      setProxies([]);
    }
  }, [nodeId]);

  const fetchData = async () => {
    if (!nodeId) return;

    setLoading(true);
    try {
      const serverResponse = await frpsAPI.getServerInfo(nodeId);
      const serverData = serverResponse.data;
      const { tcpmuxHTTPConnectPort, subdomainHost } = serverData;

      if (!tcpmuxHTTPConnectPort || tcpmuxHTTPConnectPort === 0) {
        setProxies([]);
        return;
      }

      const proxyResponse = await frpsAPI.getProxies("tcpmux", nodeId);
      const proxyData = proxyResponse.data;

      if (proxyData?.proxies) {
        const tcpmuxProxies = proxyData.proxies.map(
          (proxy: any) =>
            new TCPMuxProxy(proxy, tcpmuxHTTPConnectPort, subdomainHost)
        );
        setProxies(tcpmuxProxies);
      } else {
        setProxies([]);
      }
    } catch (error) {
      message.error("获取TCPMux代理数据失败");
      console.error("Error fetching TCPMux proxies:", error);
      setProxies([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <ProxyTable
        proxies={proxies}
        proxyType="tcpmux"
        loading={loading}
        onRefresh={fetchData}
      />
    </div>
  );
};

export default ProxiesTCPMux;
