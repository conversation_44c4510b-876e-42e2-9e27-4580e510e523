import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Select,
  Input,
  message,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  Check<PERSON>ircleOutlined,
  StopOutlined,
  GlobalOutlined,
  UserOutlined,
  NodeIndexOutlined,
} from '@ant-design/icons';
import { domainsAPI, usersAPI, frpsNodesAPI } from '@/utils/api';

const { Title } = Typography;
const { Option } = Select;

interface Domain {
  id: number;
  domain: string;
  user_id: number;
  node_id: number;
  user?: {
    id: number;
    username: string;
    email: string;
  };
  node?: {
    id: number;
    name: string;
    host: string;
  };
  status: 'active' | 'inactive' | 'pending' | 'error';
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface User {
  id: number;
  username: string;
  email: string;
  status: number;
}

interface FrpsNode {
  id: number;
  name: string;
  description: string;
  host: string;
  status: string;
  is_active: boolean;
}

interface DomainStats {
  total_domains: number;
  active_domains: number;
  inactive_domains: number;
  pending_domains: number;
  error_domains: number;
}

const Domains: React.FC = () => {
  const [domains, setDomains] = useState<Domain[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [nodes, setNodes] = useState<FrpsNode[]>([]);
  const [stats, setStats] = useState<DomainStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDomain, setEditingDomain] = useState<Domain | null>(null);
  const [form] = Form.useForm();

  // 获取域名列表
  const fetchDomains = async () => {
    setLoading(true);
    try {
      const response = await domainsAPI.getDomains();
      setDomains(response.data);
    } catch (error) {
      message.error('获取域名列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await usersAPI.getUsers();
      setUsers(response.data);
    } catch (error) {
      message.error('获取用户列表失败');
    }
  };

  // 获取节点列表
  const fetchNodes = async () => {
    try {
      const response = await frpsNodesAPI.getNodes();
      setNodes(response.data);
    } catch (error) {
      message.error('获取节点列表失败');
    }
  };

  // 获取域名统计
  const fetchStats = async () => {
    try {
      const response = await domainsAPI.getDomainStats();
      setStats(response.data);
    } catch (error) {
      message.error('获取域名统计失败');
    }
  };

  useEffect(() => {
    fetchDomains();
    fetchUsers();
    fetchNodes();
    fetchStats();
  }, []);

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      active: { color: 'green', text: '活跃' },
      inactive: { color: 'red', text: '停用' },
      pending: { color: 'orange', text: '待激活' },
      error: { color: 'red', text: '错误' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '域名',
      dataIndex: 'domain',
      key: 'domain',
      render: (domain: string) => (
        <span style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>{domain}</span>
      ),
    },
    {
      title: '用户',
      key: 'user',
      render: (record: Domain) => (
        <Space>
          <UserOutlined />
          {record.user?.username || `用户ID: ${record.user_id}`}
        </Space>
      ),
    },
    {
      title: '节点',
      key: 'node',
      render: (record: Domain) => (
        <Space>
          <NodeIndexOutlined />
          {record.node?.name || `节点ID: ${record.node_id}`}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderStatusTag,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Domain) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.status === 'active' ? (
            <Button
              type="link"
              icon={<StopOutlined />}
              onClick={() => handleDeactivate(record.id)}
            >
              停用
            </Button>
          ) : (
            <Button
              type="link"
              icon={<CheckCircleOutlined />}
              onClick={() => handleActivate(record.id)}
            >
              激活
            </Button>
          )}
          <Popconfirm
            title="确定要删除这个域名吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理创建/编辑域名
  const handleSubmit = async (values: any) => {
    try {
      if (editingDomain) {
        await domainsAPI.updateDomain(editingDomain.id, values);
        message.success('域名更新成功');
      } else {
        await domainsAPI.createDomain(values);
        message.success('域名创建成功');
      }
      setModalVisible(false);
      setEditingDomain(null);
      form.resetFields();
      fetchDomains();
      fetchStats();
    } catch (error: any) {
      message.error(error.response?.data?.message || '操作失败');
    }
  };

  // 处理编辑
  const handleEdit = (domain: Domain) => {
    setEditingDomain(domain);
    form.setFieldsValue({
      user_id: domain.user_id,
      node_id: domain.node_id,
      description: domain.description,
      status: domain.status,
    });
    setModalVisible(true);
  };

  // 处理激活
  const handleActivate = async (id: number) => {
    try {
      await domainsAPI.activateDomain(id);
      message.success('域名激活成功');
      fetchDomains();
      fetchStats();
    } catch (error) {
      message.error('域名激活失败');
    }
  };

  // 处理停用
  const handleDeactivate = async (id: number) => {
    try {
      await domainsAPI.deactivateDomain(id);
      message.success('域名停用成功');
      fetchDomains();
      fetchStats();
    } catch (error) {
      message.error('域名停用失败');
    }
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      await domainsAPI.deleteDomain(id);
      message.success('域名删除成功');
      fetchDomains();
      fetchStats();
    } catch (error) {
      message.error('域名删除失败');
    }
  };

  return (
    <div>
      <Title level={2}>
        <GlobalOutlined /> 域名管理
      </Title>

      {/* 统计卡片 */}
      {stats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="总域名数" value={stats.total_domains} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="活跃域名" 
                value={stats.active_domains} 
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="停用域名" 
                value={stats.inactive_domains} 
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="待激活域名" 
                value={stats.pending_domains} 
                valueStyle={{ color: '#d46b08' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 域名列表 */}
      <Card
        title="域名列表"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingDomain(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加域名
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={domains}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 创建/编辑域名模态框 */}
      <Modal
        title={editingDomain ? '编辑域名' : '添加域名'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingDomain(null);
          form.resetFields();
        }}
        footer={null}
      >
        <Form form={form} onFinish={handleSubmit} layout="vertical">
          {!editingDomain && (
            <>
              <Form.Item
                name="user_id"
                label="用户"
                rules={[{ required: true, message: '请选择用户' }]}
              >
                <Select placeholder="选择用户" showSearch optionFilterProp="children">
                  {users.map((user) => (
                    <Option key={user.id} value={user.id}>
                      {user.username} ({user.email})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="node_id"
                label="节点"
                rules={[{ required: true, message: '请选择节点' }]}
              >
                <Select placeholder="选择节点" showSearch optionFilterProp="children">
                  {nodes.filter(node => node.is_active).map((node) => (
                    <Option key={node.id} value={node.id}>
                      {node.name} ({node.host})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </>
          )}

          {editingDomain && (
            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select placeholder="选择状态">
                <Option value="active">活跃</Option>
                <Option value="inactive">停用</Option>
                <Option value="pending">待激活</Option>
                <Option value="error">错误</Option>
              </Select>
            </Form.Item>
          )}

          <Form.Item name="description" label="描述">
            <Input.TextArea rows={3} placeholder="域名描述（可选）" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingDomain ? '更新' : '创建'}
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingDomain(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Domains;
