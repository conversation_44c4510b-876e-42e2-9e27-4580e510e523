package main

import (
	"log"

	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
)

// InitializeDefaultData creates default services and admin user
func InitializeDefaultData() error {
	// Create default services
	services := []model.Service{
		{
			Name:        "免费版",
			Description: "基础免费服务",
			Price:       0,
			TrafficMB:   1024, // 1GB
			MaxTunnels:  2,
			Active:      true,
		},
		{
			Name:        "标准版",
			Description: "标准付费服务",
			Price:       29.9,
			TrafficMB:   10240, // 10GB
			MaxTunnels:  10,
			Active:      true,
		},
		{
			Name:        "专业版",
			Description: "专业付费服务",
			Price:       99.9,
			TrafficMB:   51200, // 50GB
			MaxTunnels:  50,
			Active:      true,
		},
	}

	for _, service := range services {
		var existingService model.Service
		result := database.DB.Where("name = ?", service.Name).First(&existingService)
		if result.Error != nil {
			// Service doesn't exist, create it
			if err := database.DB.Create(&service).Error; err != nil {
				log.Printf("Failed to create service %s: %v", service.Name, err)
			} else {
				log.Printf("Created service: %s", service.Name)
			}
		}
	}

	// Create default admin user
	var adminUser model.User
	result := database.DB.Where("role = ? AND username = ?", "admin", "admin").First(&adminUser)
	if result.Error != nil {
		// Admin user doesn't exist, create it
		admin := model.User{
			Username: "admin",
			Email:    "<EMAIL>",
			Role:     "admin",
			Status:   1,
		}

		if err := admin.SetPassword("admin123"); err != nil {
			return err
		}

		if err := database.DB.Create(&admin).Error; err != nil {
			return err
		}

		log.Printf("Created admin user: username=admin, password=admin123")
		log.Printf("Please change the admin password after first login!")
	}

	return nil
}
