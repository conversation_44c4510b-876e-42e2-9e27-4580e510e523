<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="669af56e-8060-495c-a1de-742667dda4fc" name="Changes" comment="up">
      <change beforePath="$PROJECT_DIR$/pkg/server/controller/plugin.go" beforeDir="false" afterPath="$PROJECT_DIR$/pkg/server/controller/plugin.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/sdk/go1.24.3" />
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30OpPqFflJrpNPWfxlslwGDFgq7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "git-widget-placeholder": "master",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "last_opened_file_path": "D:/Workspace/web/frp",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "D:\\Workspace\\web\\frp\\web\\node_modules\\typescript\\lib"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-33c477a475b1-e0158606a674-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-241.19072.18" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-GO-241.19072.18" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="javascript:npm:react" />
    <option featureType="dependencySupport" implementationName="javascript:npm:prettier" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vite" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vue" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="up" />
    <option name="LAST_COMMIT_MESSAGE" value="up" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>