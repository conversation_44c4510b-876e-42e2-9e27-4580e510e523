package controller

import (
	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
	"frp-panel/pkg/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AdminController struct {
	authService    *service.AuthService
	userService    *service.UserService
	trafficService *service.TrafficService
	proxyService   *service.ProxyService
}

// HandleResetUserTraffic resets the current month's traffic usage for a user
func (c *AdminController) HandleResetUserTraffic(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}
	if err := c.trafficService.ResetCurrentMonth(uint(id)); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	return map[string]string{"message": "User monthly traffic reset"}, nil
}

// HandleCloseUserProxies deactivates all proxies for a user in our DB. Optional: also close via frps admin API (future work)
func (c *AdminController) HandleCloseUserProxies(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}
	if err := c.proxyService.DeactivateAllByUser(uint(id)); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	return map[string]string{"message": "User proxies deactivated"}, nil
}

func NewAdminController(authService *service.AuthService, userService *service.UserService, trafficService *service.TrafficService, proxyService *service.ProxyService) *AdminController {
	return &AdminController{
		authService:    authService,
		userService:    userService,
		trafficService: trafficService,
		proxyService:   proxyService,
	}
}

func (c *AdminController) Register(engine *gin.Engine) {
	admin := engine.Group("/admin")
	admin.Use(c.AdminAuthMiddleware())
	{
		// Dashboard
		admin.GET("/dashboard", MakeGinHandlerFunc(c.HandleDashboard))
		admin.GET("/stats", MakeGinHandlerFunc(c.HandleGetStats))

		// User management
		admin.GET("/users", MakeGinHandlerFunc(c.HandleGetUsers))
		admin.GET("/users/:id", MakeGinHandlerFunc(c.HandleGetUser))
		admin.PUT("/users/:id", MakeGinHandlerFunc(c.HandleUpdateUser))
		admin.DELETE("/users/:id", MakeGinHandlerFunc(c.HandleDeleteUser))

		// Service management
		admin.GET("/services", MakeGinHandlerFunc(c.HandleGetServices))
		admin.POST("/services", MakeGinHandlerFunc(c.HandleCreateService))
		admin.PUT("/services/:id", MakeGinHandlerFunc(c.HandleUpdateService))
		admin.DELETE("/services/:id", MakeGinHandlerFunc(c.HandleDeleteService))

		// User service assignment
		admin.POST("/users/:id/service", MakeGinHandlerFunc(c.HandleAssignService))
		admin.DELETE("/users/:id/service", MakeGinHandlerFunc(c.HandleRemoveService))

		// Traffic management
		admin.GET("/traffic/stats", MakeGinHandlerFunc(c.HandleTrafficStats))
		admin.POST("/traffic/cleanup", MakeGinHandlerFunc(c.HandleCleanupTraffic))

		// Per-user traffic and proxy operations
		admin.POST("/users/:id/reset-traffic", MakeGinHandlerFunc(c.HandleResetUserTraffic))
		admin.POST("/users/:id/close-proxies", MakeGinHandlerFunc(c.HandleCloseUserProxies))
	}

	// Serve admin dashboard HTML
	// engine.Static("/static", "./web/static")
	// engine.LoadHTMLGlob("web/templates/*")
	admin.GET("/", c.HandleAdminDashboardPage)
}

// AdminAuthMiddleware validates admin access
func (c *AdminController) AdminAuthMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		token := ctx.GetHeader("Authorization")
		if token == "" {
			// Try to get token from cookie for web interface
			token, _ = ctx.Cookie("admin_token")
		}

		if token == "" {
			ctx.JSON(http.StatusUnauthorized, &Response{Msg: "Authorization required"})
			ctx.Abort()
			return
		}

		// Remove "Bearer " prefix if present
		if len(token) > 7 && token[:7] == "Bearer " {
			token = token[7:]
		}

		user, err := c.authService.GetUserByToken(token)
		if err != nil || user.Role != "admin" {
			ctx.JSON(http.StatusForbidden, &Response{Msg: "Admin access required"})
			ctx.Abort()
			return
		}

		ctx.Set("admin", user)
		ctx.Next()
	}
}

func (c *AdminController) HandleAdminDashboardPage(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin_dashboard.html", gin.H{
		"title": "FRP Multi-User Admin Dashboard",
	})
}

func (c *AdminController) HandleDashboard(ctx *gin.Context) (interface{}, error) {
	dashboardStats, err := c.userService.GetDashboardStats()
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	trafficStats, err := c.trafficService.GetTrafficStats()
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return map[string]interface{}{
		"dashboard": dashboardStats,
		"traffic":   trafficStats,
	}, nil
}

func (c *AdminController) HandleGetStats(ctx *gin.Context) (interface{}, error) {
	return c.userService.GetDashboardStats()
}

func (c *AdminController) HandleGetUsers(ctx *gin.Context) (interface{}, error) {
	users, err := c.userService.GetAllUsers()
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}
	return users, nil
}

func (c *AdminController) HandleGetUser(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	user, err := c.userService.GetUserByID(uint(id))
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusNotFound,
			Err:  err,
		}
	}

	return user, nil
}

func (c *AdminController) HandleUpdateUser(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	user, err := c.userService.GetUserByID(uint(id))
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusNotFound,
			Err:  err,
		}
	}

	var req struct {
		Username string `json:"username"`
		Email    string `json:"email"`
		Status   *int   `json:"status"`
		Role     string `json:"role"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if req.Username != "" {
		user.Username = req.Username
	}
	if req.Email != "" {
		user.Email = req.Email
	}
	if req.Status != nil {
		user.Status = *req.Status
	}
	if req.Role != "" {
		user.Role = req.Role
	}

	if err := c.userService.UpdateUser(user); err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return user, nil
}

func (c *AdminController) HandleDeleteUser(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if err := c.userService.DeleteUser(uint(id)); err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return map[string]string{"message": "User deleted successfully"}, nil
}

func (c *AdminController) HandleGetServices(ctx *gin.Context) (interface{}, error) {
	var services []model.Service
	result := database.DB.Find(&services)
	return services, result.Error
}

func (c *AdminController) HandleCreateService(ctx *gin.Context) (interface{}, error) {
	var req model.Service
	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if err := database.DB.Create(&req).Error; err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return req, nil
}

func (c *AdminController) HandleUpdateService(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	var service model.Service
	if err := database.DB.First(&service, uint(id)).Error; err != nil {
		return nil, &HTTPError{
			Code: http.StatusNotFound,
			Err:  err,
		}
	}

	if err := ctx.ShouldBindJSON(&service); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if err := database.DB.Save(&service).Error; err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return service, nil
}

func (c *AdminController) HandleDeleteService(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if err := database.DB.Delete(&model.Service{}, uint(id)).Error; err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return map[string]string{"message": "Service deleted successfully"}, nil
}

func (c *AdminController) HandleAssignService(ctx *gin.Context) (interface{}, error) {
	userIDStr := ctx.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	var req struct {
		ServiceID uint `json:"service_id" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if err := c.userService.PurchaseService(uint(userID), req.ServiceID); err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return map[string]string{"message": "Service assigned successfully"}, nil
}

func (c *AdminController) HandleRemoveService(ctx *gin.Context) (interface{}, error) {
	userIDStr := ctx.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if err := c.userService.CancelService(uint(userID)); err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return map[string]string{"message": "Service removed successfully"}, nil
}

func (c *AdminController) HandleTrafficStats(ctx *gin.Context) (interface{}, error) {
	return c.trafficService.GetTrafficStats()
}

func (c *AdminController) HandleCleanupTraffic(ctx *gin.Context) (interface{}, error) {
	var req struct {
		MonthsToKeep int `json:"months_to_keep" binding:"required,min=1"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if err := c.trafficService.CleanupOldTraffic(req.MonthsToKeep); err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return map[string]string{"message": "Traffic cleanup completed successfully"}, nil
}
