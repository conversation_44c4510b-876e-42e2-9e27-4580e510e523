serverAddr = "127.0.0.1"
serverPort = 7000
metadatas.username = "admin"
metadatas.token = "614aace37449f1816b48ab45f93f99c0a0a1a1b890cec6350563ec2b33c1fb1b"

[[proxies]]
name = "test-tcp"
type = "tcp"
localIP = "127.0.0.1"
localPort = 22
remotePort = 6000
metadatas.username = "admin"
metadatas.token = "614aace37449f1816b48ab45f93f99c0a0a1a1b890cec6350563ec2b33c1fb1b"

[[proxies]]
name = "web-http"
type = "http"
localIP = "127.0.0.1"
localPort = 80
customDomains = ["test.localhost"]
metadatas.username = "admin"
metadatas.token = "614aace37449f1816b48ab45f93f99c0a0a1a1b890cec6350563ec2b33c1fb1b"

[webServer]
addr = "0.0.0.0"
port = 7400
user = "admin"
password = "admin"
