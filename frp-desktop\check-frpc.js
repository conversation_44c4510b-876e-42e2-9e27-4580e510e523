#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🔍 检查 FRPC 二进制文件...\n')

const isWindows = process.platform === 'win32'
const binaryNames = isWindows ? ['frpc.exe', 'frpc'] : ['frpc', 'frpc.exe']

// 可能的基础路径
const basePaths = [
  path.join(__dirname, '../bin'),
  path.join(__dirname, '../../bin'),
  path.join(process.cwd(), 'bin'),
  path.join(process.cwd(), '../bin')
]

// 生成所有可能的路径组合
const possiblePaths = []
for (const basePath of basePaths) {
  for (const binaryName of binaryNames) {
    possiblePaths.push(path.join(basePath, binaryName))
  }
}

console.log('🔍 搜索路径:')
possiblePaths.forEach((p, i) => {
  console.log(`  ${i + 1}. ${p}`)
})

console.log('\n📋 检查结果:')

let found = false
for (const binPath of possiblePaths) {
  const exists = fs.existsSync(binPath)
  console.log(`${exists ? '✅' : '❌'} ${binPath}`)
  
  if (exists && !found) {
    found = true
    console.log(`\n🎉 找到 FRPC 二进制文件: ${binPath}`)
    
    // 检查文件权限
    try {
      const stats = fs.statSync(binPath)
      console.log(`📊 文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`)
      console.log(`📅 修改时间: ${stats.mtime.toLocaleString()}`)
      
      // 在非 Windows 系统上检查执行权限
      if (!isWindows) {
        const mode = stats.mode
        const isExecutable = (mode & parseInt('111', 8)) !== 0
        console.log(`🔐 可执行权限: ${isExecutable ? '是' : '否'}`)
        
        if (!isExecutable) {
          console.log('⚠️  警告: 文件没有执行权限，请运行: chmod +x ' + binPath)
        }
      }
    } catch (error) {
      console.log(`❌ 无法读取文件信息: ${error.message}`)
    }
  }
}

if (!found) {
  console.log('\n❌ 未找到 FRPC 二进制文件!')
  console.log('\n💡 解决方案:')
  console.log('1. 从 https://github.com/fatedier/frp/releases 下载 frp')
  console.log('2. 解压后将 frpc 文件复制到 bin/ 目录')
  console.log('3. 确保文件名正确:')
  console.log(`   - Windows: frpc.exe`)
  console.log(`   - Linux/macOS: frpc`)
  
  process.exit(1)
} else {
  console.log('\n✅ FRPC 二进制文件检查完成!')
}
