package config

import (
	"fmt"
	"io/ioutil"
	"os"

	"gopkg.in/yaml.v2"
)

type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Auth     AuthConfig     `yaml:"auth"`
	Admin    AdminConfig    `yaml:"admin"`
	Domain   DomainConfig   `yaml:"domain"`
}

type ServerConfig struct {
	BindAddress string `yaml:"bind_address"`
	Port        int    `yaml:"port"`
}

type DatabaseConfig struct {
	Type string `yaml:"type"`
	DSN  string `yaml:"dsn"`
}

type AuthConfig struct {
	APIURL          string `yaml:"api_url"`
	JWTSecret       string `yaml:"jwt_secret"`
	TokenTTLMinutes int    `yaml:"token_ttl_minutes"`
}

type AdminConfig struct {
	BaseURL  string `yaml:"base_url"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

type DomainConfig struct {
	BaseDomain string `yaml:"base_domain"`
}

func LoadConfig(configPath string) (*Config, error) {
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("config file not found: %s", configPath)
	}

	// 读取配置文件
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	// 解析YAML配置
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %v", err)
	}

	// 设置默认值
	if config.Server.BindAddress == "" {
		config.Server.BindAddress = "127.0.0.1"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 7200
	}
	if config.Database.Type == "" {
		config.Database.Type = "sqlite"
	}
	if config.Database.DSN == "" {
		config.Database.DSN = "./users.db"
	}
	if config.Auth.JWTSecret == "" {
		config.Auth.JWTSecret = "change-me-secret"
	}
	if config.Auth.TokenTTLMinutes == 0 {
		config.Auth.TokenTTLMinutes = 60 * 24 // 1 day
	}
	if config.Admin.BaseURL == "" {
		// Default frps admin API base (native frps default 7500). You can front this via /admin/frps/* if preferred.
		config.Admin.BaseURL = "http://127.0.0.1:7500/api"
	}
	if config.Domain.BaseDomain == "" {
		config.Domain.BaseDomain = "example.com"
	}

	return &config, nil
}

func (c *Config) GetBindAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.BindAddress, c.Server.Port)
}
