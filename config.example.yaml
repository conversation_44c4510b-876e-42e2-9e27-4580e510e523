# 服务器配置
server:
  bind_address: "0.0.0.0"  # 绑定地址
  port: 7200               # 监听端口

# 数据库配置
database:
  type: "sqlite"           # 数据库类型，目前支持 sqlite
  dsn: "./data/users.db"   # 数据库连接字符串

# 认证配置
auth:
  api_url: "https://your-auth-api.com"  # 外部认证API地址
  jwt_secret: "your-jwt-secret-key"     # JWT密钥
  token_ttl_minutes: 1440               # Token有效期（分钟）

# 管理员配置
admin:
  base_url: "http://127.0.0.1:7500/api" # FRP服务器管理API地址
  username: "admin"                     # 管理员用户名
  password: "admin"                     # 管理员密码

# 域名配置
domain:
  base_domain: "example.com"            # 基础域名，用户域名格式为 username.nodename.example.com