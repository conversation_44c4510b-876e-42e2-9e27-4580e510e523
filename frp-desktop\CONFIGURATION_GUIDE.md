# FRP Desktop Client 配置指南

## 📁 配置文件位置

您的FRPC配置文件位于: `conf/frpc.toml`

应用已自动配置为使用此文件，无需手动设置路径。

## ✅ 配置验证

### 1. 检查配置文件解析
```bash
npm run test-config
```

### 2. 验证FRPC二进制文件
```bash
npm run check-frpc
```

### 3. 测试FRPC启动
```bash
../bin/frpc -c ../conf/frpc.toml
```

## 📋 当前配置信息

根据您的 `conf/frpc.toml` 文件：

- **服务器地址**: frp.freefrp.net
- **服务器端口**: 7000
- **用户名**: 已配置
- **Token**: 已配置
- **隧道数量**: 2个
  1. **ssh** (tcp) - 127.0.0.1:22 → 远程端口: 10022
  2. **web** (http) - 127.0.0.1:80 → 自定义域名: test.frp.freefrp.net

## 🚀 使用步骤

### 1. 启动应用
```bash
npm run electron:dev
```

### 2. 登录系统
- 注册新账户或使用现有账户登录

### 3. 验证配置
- 应用会自动加载 `conf/frpc.toml` 配置
- 在配置页面可以查看和修改设置

### 4. 启动服务
- 在控制台页面点击"启动 FRPC 服务"
- 观察日志输出，确认连接状态

### 5. 监控状态
- 实时查看连接状态
- 监控隧道运行情况
- 查看详细日志信息

## 🔧 配置文件格式

您的配置文件使用新版TOML格式：

```toml
serverAddr = "frp.freefrp.net"
serverPort = 7000
metadatas.token = "your-token"
metadatas.username = "your-username"

[[proxies]]
name = "ssh"
type = "tcp"
localIP = "127.0.0.1"
localPort = 22
remotePort = 10022
metadatas.token = "your-token"
metadatas.username = "your-username"

[[proxies]]
name = "web"
type = "http"
localIP = "127.0.0.1"
localPort = 80
customDomains = ["test.frp.freefrp.net"]
metadatas.token = "your-token"
metadatas.username = "your-username"
```

## 🎯 功能特点

### 自动配置加载
- ✅ 自动检测 `conf/frpc.toml` 文件
- ✅ 解析新版TOML格式
- ✅ 支持多个隧道配置
- ✅ 保留原有配置结构

### 智能路径处理
- ✅ 开发模式使用项目根目录的 `conf/` 文件夹
- ✅ 生产模式使用用户数据目录
- ✅ 自动创建必要的目录结构

### 配置同步
- ✅ 界面修改会更新配置文件
- ✅ 配置文件修改会反映到界面
- ✅ 支持配置导入导出

## 🐛 故障排除

### 问题1: "配置文件不存在"
**解决方案**: 确认 `conf/frpc.toml` 文件存在且可读

### 问题2: "配置解析失败"
**解决方案**: 
1. 运行 `npm run test-config` 检查语法
2. 确认TOML格式正确
3. 检查特殊字符和编码

### 问题3: "连接失败"
**可能原因**:
- Token无效或过期
- 服务器地址错误
- 网络连接问题
- 防火墙阻止

**解决方案**:
1. 检查token是否正确
2. 确认服务器地址和端口
3. 测试网络连接
4. 查看详细日志信息

### 问题4: "隧道无法建立"
**解决方案**:
1. 检查本地服务是否运行
2. 确认端口配置正确
3. 验证域名解析
4. 查看FRPC日志

## 📝 日志分析

### 正常启动日志
```
[I] start frpc service for config file [../conf/frpc.toml]
[I] try to connect to server...
[I] admin server listen on 0.0.0.0:7400
[I] login to server success, get run id [xxx]
[I] start proxy success
```

### 常见错误日志
- `invalid token`: Token无效，需要更新
- `connect to server error`: 服务器连接失败
- `port already used`: 端口被占用
- `proxy [xxx] start error`: 特定隧道启动失败

## 🆘 获取帮助

如果遇到问题：
1. 运行 `npm run test-config` 验证配置
2. 查看应用日志获取详细信息
3. 检查网络连接和防火墙设置
4. 联系FRP服务提供商确认token状态

---

**提示**: 配置文件已正确设置，您可以直接启动应用开始使用！
