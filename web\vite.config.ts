import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"), // 将@指向src目录
    },
  },
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      "/api": {
        target: "http://localhost:7200",
        changeOrigin: true,
        secure: false,
      },
       "/admin": {
        target: "http://localhost:7200",
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    outDir: "build",
    sourcemap: true,
  },
});
