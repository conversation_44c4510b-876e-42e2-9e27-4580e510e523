package model

import "time"

// Service represents a FRP service plan
type Service struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	Name        string    `gorm:"not null;size:100;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"name"`
	Description string    `gorm:"type:text;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"description"`
	Price       float64   `gorm:"not null;type:decimal(10,2)" json:"price"`
	TrafficMB   int64     `gorm:"not null" json:"traffic_mb"` // Monthly traffic limit in MB
	MaxTunnels  int       `gorm:"default:5" json:"max_tunnels"`
	Active      bool      `gorm:"default:true" json:"active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
