package controller

import (
	"encoding/json"
	"fmt"
	"net/http"

	"frp-panel/pkg/authz"
	"frp-panel/pkg/database"
	"frp-panel/pkg/model"

	"github.com/gin-gonic/gin"
)

// RBACController provides endpoints to manage Casbin policies and user-role assignments
type RBACController struct{}

func NewRBACController() *RBACController { return &RBACController{} }

func (c *RBACController) Register(engine *gin.Engine) {
	// Temporarily mark admin for RBAC routes to pass Casbin (until real admin auth wired)
	admin := engine.Group("/admin")
	admin.Use(func(ctx *gin.Context) {
		ctx.Set("admin", model.User{ID: 0, Role: "admin"})
		ctx.Next()
	})
	r := admin.Group("/rbac")
	r.GET("/policies", MakeGinHandlerFunc(c.HandleGetPolicies))
	r.POST("/policies", MakeGinHandlerFunc(c.<PERSON>ddPolicy))
	r.DELETE("/policies", MakeGinHandlerFunc(c.HandleRemovePolicy))

	r.P<PERSON>("/roles", MakeGinHandlerFunc(c.HandleAddRoleForUser))
	r.DELETE("/roles", MakeGinHandlerFunc(c.HandleRemoveRoleForUser))
	r.GET("/roles/:user", MakeGinHandlerFunc(c.HandleGetRolesForUser))
	r.POST("/reload", MakeGinHandlerFunc(c.HandleReloadPolicies))
}

func (c *RBACController) HandleGetPolicies(ctx *gin.Context) (interface{}, error) {
	return authz.GetPolicies()
}

func (c *RBACController) HandleAddPolicy(ctx *gin.Context) (interface{}, error) {
	var req struct {
		Sub string `json:"sub" binding:"required"`
		Obj string `json:"obj" binding:"required"`
		Act string `json:"act" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}
	ok, err := authz.AddPolicy(req.Sub, req.Obj, req.Act)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	c.logAudit(ctx, "policy_add", req)
	return gin.H{"added": ok}, nil
}

func (c *RBACController) HandleRemovePolicy(ctx *gin.Context) (interface{}, error) {
	var req struct {
		Sub string `json:"sub" binding:"required"`
		Obj string `json:"obj" binding:"required"`
		Act string `json:"act" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}
	ok, err := authz.RemovePolicy(req.Sub, req.Obj, req.Act)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	c.logAudit(ctx, "policy_remove", req)
	return gin.H{"removed": ok}, nil
}

func (c *RBACController) HandleAddRoleForUser(ctx *gin.Context) (interface{}, error) {
	var req struct {
		User string `json:"user" binding:"required"`
		Role string `json:"role" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}
	ok, err := authz.AddRoleForUser(req.User, req.Role)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	c.logAudit(ctx, "role_add", req)
	return gin.H{"added": ok}, nil
}

func (c *RBACController) HandleRemoveRoleForUser(ctx *gin.Context) (interface{}, error) {
	var req struct {
		User string `json:"user" binding:"required"`
		Role string `json:"role" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}
	ok, err := authz.RemoveRoleForUser(req.User, req.Role)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	c.logAudit(ctx, "role_remove", req)
	return gin.H{"removed": ok}, nil
}

func (c *RBACController) HandleGetRolesForUser(ctx *gin.Context) (interface{}, error) {
	user := ctx.Param("user")
	return authz.GetRolesForUser(user), nil
}

func (c *RBACController) HandleReloadPolicies(ctx *gin.Context) (interface{}, error) {
	if err := authz.Enforcer().LoadPolicy(); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	c.logAudit(ctx, "reload", map[string]string{"msg": "policy reloaded"})
	return gin.H{"reloaded": true}, nil
}

// logAudit writes an RBACAudit record with actor from context
func (c *RBACController) logAudit(ctx *gin.Context, action string, payload interface{}) {
	b, _ := json.Marshal(payload)
	actor := "unknown"
	if a, ok := ctx.Get("admin"); ok && a != nil {
		if u, ok := a.(model.User); ok {
			if u.Username != "" {
				actor = u.Username
			} else {
				actor = fmt.Sprintf("admin#%d", u.ID)
			}
		}
	} else if u, ok := ctx.Get("user"); ok && u != nil {
		if usr, ok := u.(*model.User); ok && usr != nil {
			if usr.Username != "" {
				actor = usr.Username
			} else {
				actor = fmt.Sprintf("user#%d", usr.ID)
			}
		}
	}
	_ = database.DB.Create(&model.RBACAudit{Action: action, Detail: string(b), Actor: actor}).Error
}
