import React, { useState } from "react";
import { Card, Tabs, Space } from "antd";
import { DatabaseOutlined } from "@ant-design/icons";
import ProxiesHTTP from "./components/ProxiesHTTP";
import ProxiesHTTPS from "./components/ProxiesHTTPS";
import ProxiesTCP from "./components/ProxiesTCP";
import ProxiesUDP from "./components/ProxiesUDP";
import ProxiesSTCP from "./components/ProxiesSTCP";
import ProxiesSUDP from "./components/ProxiesSUDP";
import ProxiesTCPMux from "./components/ProxiesTCPMux";
import NodeSelector from "@/components/NodeSelector";

interface FrpsNode {
  id: number;
  name: string;
  description: string;
  host: string;
  port: number;
  admin_port: number;
  status: 'online' | 'offline' | 'error' | 'unknown';
  is_active: boolean;
}

const FrpsProxies: React.FC = () => {
  const [selectedNodeId, setSelectedNodeId] = useState<number | undefined>(undefined);
  const [selectedNode, setSelectedNode] = useState<FrpsNode | null>(null);

  const handleNodeChange = (nodeId: number, node: FrpsNode) => {
    setSelectedNodeId(nodeId);
    setSelectedNode(node);
  };

  const tabItems = [
    {
      key: "tcp",
      label: "TCP",
      children: <ProxiesTCP nodeId={selectedNodeId} />,
    },
    {
      key: "udp",
      label: "UDP",
      children: <ProxiesUDP nodeId={selectedNodeId} />,
    },
    {
      key: "http",
      label: "HTTP",
      children: <ProxiesHTTP nodeId={selectedNodeId} />,
    },
    {
      key: "https",
      label: "HTTPS",
      children: <ProxiesHTTPS nodeId={selectedNodeId} />,
    },
    {
      key: "tcpmux",
      label: "TCPMux",
      children: <ProxiesTCPMux nodeId={selectedNodeId} />,
    },
    {
      key: "stcp",
      label: "STCP",
      children: <ProxiesSTCP nodeId={selectedNodeId} />,
    },
    {
      key: "sudp",
      label: "SUDP",
      children: <ProxiesSUDP nodeId={selectedNodeId} />,
    },
  ];

  return (
    <div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: 24,
        }}
      >
        <div>
          <h2>
            <DatabaseOutlined style={{ marginRight: 8 }} />
            FRP 代理管理
            {selectedNode && (
              <span style={{ fontSize: '14px', fontWeight: 'normal', color: '#666', marginLeft: '8px' }}>
                - {selectedNode.name}
              </span>
            )}
          </h2>
        </div>
        <Space>
          <NodeSelector
            value={selectedNodeId}
            onChange={handleNodeChange}
            placeholder="选择节点"
            style={{ width: 250 }}
          />
        </Space>
      </div>

      {!selectedNodeId && (
        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0', color: '#999' }}>
            <DatabaseOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <div style={{ fontSize: '16px' }}>请选择一个节点查看代理信息</div>
          </div>
        </Card>
      )}

      {selectedNodeId && (
        <Card>
          <Tabs items={tabItems} defaultActiveKey="tcp" />
        </Card>
      )}
    </div>
  );
};

export default FrpsProxies;
