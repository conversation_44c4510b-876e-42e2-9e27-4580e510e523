package service

import (
	"errors"

	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
)

// AuthenticatedUser is a minimal view used by plugin controller
// to decide on limits without pulling entire model into controller layer.
type AuthenticatedUser struct {
	ID         uint
	Username   string
	MaxTunnels int
}

type ProxyService struct{}

func NewProxyService() *ProxyService { return &ProxyService{} }

// CountActiveByUser returns number of active proxies for a user
func (s *ProxyService) CountActiveByUser(userID uint) (int, error) {
	var count int64
	if err := database.DB.Model(&model.Proxy{}).Where("user_id = ? AND active = ?", userID, true).Count(&count).Error; err != nil {
		return 0, err
	}
	return int(count), nil
}

// EnsureProxy creates or re-activates a proxy record for this user
func (s *ProxyService) EnsureProxy(userID uint, name, ptype string) error {
	if name == "" {
		return errors.New("empty proxy name")
	}
	var p model.Proxy
	res := database.DB.Where("user_id = ? AND name = ?", userID, name).First(&p)
	if res.Error == nil {
		p.Active = true
		if ptype != "" {
			p.Type = ptype
		}
		return database.DB.Save(&p).Error
	}
	// create new
	p = model.Proxy{UserID: userID, Name: name, Type: ptype, Active: true}
	return database.DB.Create(&p).Error
}

// DeactivateAllByUser marks all proxies as inactive (optional helper)
func (s *ProxyService) DeactivateAllByUser(userID uint) error {
	return database.DB.Model(&model.Proxy{}).Where("user_id = ?", userID).Update("active", false).Error
}
