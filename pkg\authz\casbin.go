package authz

import (
	"strings"

	appmodel "frp-panel/pkg/model"

	casbin "github.com/casbin/casbin/v2"
	casbinmodel "github.com/casbin/casbin/v2/model"
	adapter "github.com/casbin/gorm-adapter/v3"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var enforcer *casbin.Enforcer

// Default RBAC model with path and method matchers
const modelText = `
[request_definition]
 r = sub, obj, act

[policy_definition]
 p = sub, obj, act

[role_definition]
 g = _, _

[policy_effect]
 e = some(where (p.eft == allow))

[matchers]
 m = (r.sub == p.sub || g(r.sub, p.sub)) && keyMatch2(r.obj, p.obj) && regexMatch(r.act, p.act)
`

// Init initializes Casbin with GORM adapter and creates default policies if needed.
func Init(db *gorm.DB) error {
	m, err := casbinmodel.NewModelFromString(modelText)
	if err != nil {
		return err
	}
	adp, err := adapter.NewAdapterByDBUseTableName(db, "", "casbin_rule")
	if err != nil {
		return err
	}
	e, err := casbin.NewEnforcer(m, adp)
	if err != nil {
		return err
	}
	// Load policies from DB
	if err := e.LoadPolicy(); err != nil {
		return err
	}
	// Seed a basic admin policy if none exists
	pols, err := e.GetPolicy()
	if err != nil {
		return err
	}
	if len(pols) == 0 {
		// admin can access /admin/** with any method
		_, _ = e.AddPolicy("admin", "/admin/*", "(GET|POST|PUT|DELETE)")
		// users can access protected /api/** GET by default (customize as needed)
		_, _ = e.AddPolicy("user", "/api/*", "(GET|POST|PUT)")
		_ = e.SavePolicy()
	}
	enforcer = e
	return nil
}

func Enforcer() *casbin.Enforcer { return enforcer }

// SubjectFromContext extracts subject (role or username) from gin context.
// Priority: admin role -> "admin"; else user role or username -> "user".
func SubjectFromContext(c *gin.Context) string {
	// If admin middleware set an admin user
	if a, ok := c.Get("admin"); ok && a != nil {
		return "admin"
	}
	if u, ok := c.Get("user"); ok && u != nil {
		if usr, ok := u.(*appmodel.User); ok && usr != nil {
			// If authenticated user itself is admin, treat subject as admin
			if usr.Role == "admin" {
				return "admin"
			}
			// Prefer concrete role if any
			if usr.Role != "" {
				return usr.Role
			}
			// Fall back to username for per-user policies or grouping policies
			if usr.Username != "" {
				return usr.Username
			}
		}
		// fallback generic user
		return "user"
	}
	return "anonymous"
}

// CasbinMiddleware enforces RBAC policies on admin routes.
func CasbinMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Allow public endpoints
		path := c.Request.URL.Path
		if strings.HasPrefix(path, "/api/login") || strings.HasPrefix(path, "/api/register") || strings.HasPrefix(path, "/handler") || strings.HasPrefix(path, "/admin/") {
			c.Next()
			return
		}
		// Allow CORS preflight
		if c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}
		sub := SubjectFromContext(c)
		obj := path
		act := c.Request.Method
		if enforcer == nil {
			c.Next()
			return
		}
		ok, err := enforcer.Enforce(sub, obj, act)
		if err != nil {
			c.AbortWithStatusJSON(500, gin.H{"msg": err.Error()})
			return
		}
		if !ok {
			c.AbortWithStatusJSON(403, gin.H{"msg": "forbidden"})
			return
		}
		c.Next()
	}
}

// Management helpers
func AddPolicy(sub, obj, act string) (bool, error)    { return enforcer.AddPolicy(sub, obj, act) }
func RemovePolicy(sub, obj, act string) (bool, error) { return enforcer.RemovePolicy(sub, obj, act) }
func AddRoleForUser(user, role string) (bool, error)  { return enforcer.AddGroupingPolicy(user, role) }
func RemoveRoleForUser(user, role string) (bool, error) {
	return enforcer.RemoveGroupingPolicy(user, role)
}
func GetPolicies() ([][]string, error)     { return enforcer.GetPolicy() }
func GetRolesForUser(user string) []string { roles, _ := enforcer.GetRolesForUser(user); return roles }
