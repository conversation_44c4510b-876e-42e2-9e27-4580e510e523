package model

import (
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type User struct {
	ID           uint           `gorm:"primarykey" json:"id"`
	Username     string         `gorm:"uniqueIndex;not null;size:100;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"username"`
	Email        string         `gorm:"uniqueIndex;not null;size:255;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"email"`
	PasswordHash string         `gorm:"not null;size:255" json:"-"`
	Token        string         `gorm:"size:500" json:"token"`
	RunID        string         `gorm:"size:100" json:"run_id"`             // Add this field to store the FRP run_id
	Status       int            `gorm:"default:1" json:"status"`            // 1: active, 0: inactive
	Role         string         `gorm:"default:'user';size:20" json:"role"` // admin, user
	ServiceID    *uint          `json:"service_id"`
	Service      *Service       `gorm:"foreignKey:ServiceID" json:"service,omitempty"`
	TrafficUsage []TrafficUsage `gorm:"foreignKey:UserID" json:"traffic_usage,omitempty"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

// SetPassword hashes and sets the user's password
func (u *User) SetPassword(password string) error {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.PasswordHash = string(hash)
	return nil
}

// CheckPassword verifies the provided password against the stored hash
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

// GetMonthlyTrafficMB returns the user's traffic usage for the current month in MB
func (u *User) GetMonthlyTrafficMB() int64 {
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	var totalBytes int64
	for _, usage := range u.TrafficUsage {
		if usage.Date.After(startOfMonth) {
			totalBytes += usage.Bytes
		}
	}
	return totalBytes / (1024 * 1024) // Convert to MB
}

// IsTrafficExceeded checks if user has exceeded their monthly traffic limit
func (u *User) IsTrafficExceeded() bool {
	if u.Service == nil {
		return true // No service = no access
	}
	return u.GetMonthlyTrafficMB() >= u.Service.TrafficMB
}

// CanCreateTunnel checks if user can create more tunnels
func (u *User) CanCreateTunnel(currentTunnels int) bool {
	if u.Service == nil {
		return false
	}
	return currentTunnels < u.Service.MaxTunnels && !u.IsTrafficExceeded()
}
