package service

import (
	"bytes"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"frp-panel/pkg/database"
	"frp-panel/pkg/model"

	"gorm.io/gorm"
)

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResponse struct {
	Success bool   `json:"success"`
	Token   string `json:"token"`
	User    struct {
		Username string `json:"username"`
		Email    string `json:"email"`
	} `json:"user"`
}

type AuthService struct {
	apiURL string
	client *http.Client
}

func NewAuthService(apiURL string) *AuthService {
	return &AuthService{
		apiURL: apiURL,
		client: &http.Client{Timeout: 10 * time.Second},
	}
}

// LoginWithEmail authenticates user with email and password
func (s *AuthService) LoginWithEmail(email, password string) (*model.User, error) {
	var user model.User
	result := database.DB.Preload("Service").Where("email = ? AND status = 1", email).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid email or password")
		}
		return nil, result.Error
	}

	if !user.CheckPassword(password) {
		return nil, errors.New("invalid email or password")
	}

	// Generate new token
	token, err := s.generateToken()
	if err != nil {
		return nil, err
	}

	user.Token = token
	database.DB.Save(&user)

	return &user, nil
}

// Register creates a new user account
func (s *AuthService) Register(username, email, password string) (*model.User, error) {
	// Check if user already exists
	var existingUser model.User
	result := database.DB.Where("email = ? OR username = ?", email, username).First(&existingUser)
	if result.Error == nil {
		return nil, errors.New("user with this email or username already exists")
	}

	user := &model.User{
		Username: username,
		Email:    email,
		Status:   1,
		Role:     "user",
	}

	if err := user.SetPassword(password); err != nil {
		return nil, err
	}

	token, err := s.generateToken()
	if err != nil {
		return nil, err
	}
	user.Token = token

	if err := database.DB.Create(user).Error; err != nil {
		return nil, err
	}

	return user, nil
}

// Legacy Login method for backward compatibility
func (s *AuthService) Login(username, password string) (*model.User, error) {
	// Try local authentication first
	user, err := s.LoginWithEmail(username, password)
	if err == nil {
		return user, nil
	}

	// Fallback to external API if configured
	if s.apiURL == "" {
		return nil, err
	}

	return s.loginWithExternalAPI(username, password)
}

func (s *AuthService) loginWithExternalAPI(username, password string) (*model.User, error) {
	loginReq := LoginRequest{
		Username: username,
		Password: password,
	}

	jsonData, _ := json.Marshal(loginReq)
	resp, err := s.client.Post(s.apiURL+"/api/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("login failed with status: %d", resp.StatusCode)
	}

	var loginResp LoginResponse
	if err := json.NewDecoder(resp.Body).Decode(&loginResp); err != nil {
		return nil, err
	}

	if !loginResp.Success {
		return nil, fmt.Errorf("login failed")
	}

	// 保存或更新用户信息到数据库
	user := &model.User{
		Username: loginResp.User.Username,
		Token:    loginResp.Token,
		Email:    loginResp.User.Email,
		Status:   1,
		Role:     "user",
	}

	// 查找现有用户或创建新用户
	var existingUser model.User
	result := database.DB.Where("username = ?", user.Username).First(&existingUser)
	if result.Error == nil {
		// 更新现有用户
		existingUser.Token = user.Token
		existingUser.Email = user.Email
		database.DB.Save(&existingUser)
		return &existingUser, nil
	} else {
		// 创建新用户
		database.DB.Create(user)
		return user, nil
	}
}

func (s *AuthService) ValidateToken(username, token string) bool {
	var user model.User
	result := database.DB.Preload("Service").Where("username = ? AND token = ? AND status = 1", username, token).First(&user)
	if result.Error != nil {
		return false
	}

	// Check if user's service is active and not exceeded
	if user.IsTrafficExceeded() {
		return false
	}

	return true
}

// GetUserByToken retrieves user by token
func (s *AuthService) GetUserByToken(token string) (*model.User, error) {
	var user model.User
	result := database.DB.Preload("Service").Preload("TrafficUsage").Where("token = ? AND status = 1", token).First(&user)
	if result.Error != nil {
		return nil, result.Error
	}
	return &user, nil
}

// GetUserByRunID retrieves user by FRP run_id
func (s *AuthService) GetUserByRunID(runID string) (*model.User, error) {
	var user model.User
	result := database.DB.Preload("Service").Preload("TrafficUsage").Where("run_id = ? AND status = 1", runID).First(&user)
	if result.Error != nil {
		return nil, result.Error
	}
	return &user, nil
}

// generateToken creates a random token
func (s *AuthService) generateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
