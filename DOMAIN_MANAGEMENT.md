# 域名管理功能说明

## 功能概述

域名管理功能允许管理员为用户在特定节点上分配域名，域名格式为：`username.nodename.基础域名`

例如：如果用户名为 `john`，节点名为 `node1`，基础域名为 `example.com`，则生成的域名为 `john.node1.example.com`

## 配置说明

### 1. 配置文件设置

在 `config.yaml` 中添加域名配置：

```yaml
domain:
  base_domain: "example.com"  # 替换为你的实际域名
```

### 2. 数据库迁移

系统会自动创建 `domains` 表来存储域名信息，包含以下字段：
- `id`: 域名ID
- `domain`: 完整域名
- `user_id`: 用户ID
- `node_id`: 节点ID
- `status`: 域名状态（active/inactive/pending/error）
- `description`: 域名描述
- `is_active`: 是否激活

## 使用方法

### 1. 管理员操作

#### 添加域名
1. 登录管理后台
2. 进入"FRPS 管理" -> "域名管理"
3. 点击"添加域名"按钮
4. 选择用户和节点
5. 填写描述（可选）
6. 点击"创建"

#### 管理域名
- **激活域名**: 点击"激活"按钮
- **停用域名**: 点击"停用"按钮
- **编辑域名**: 点击"编辑"按钮修改状态和描述
- **删除域名**: 点击"删除"按钮（需确认）

### 2. API接口

#### 管理员接口
- `GET /admin/domains` - 获取所有域名
- `POST /admin/domains` - 创建域名
- `GET /admin/domains/:id` - 获取单个域名
- `PUT /admin/domains/:id` - 更新域名
- `DELETE /admin/domains/:id` - 删除域名
- `POST /admin/domains/:id/activate` - 激活域名
- `POST /admin/domains/:id/deactivate` - 停用域名
- `GET /admin/domains/stats` - 获取域名统计
- `POST /admin/domains/check-conflict` - 检查域名冲突

#### 公共接口
- `GET /api/domains/lookup/:domain` - 通过域名查询用户信息

### 3. 业务流程

#### 域名分配流程
1. 管理员在后台为用户分配域名
2. 系统自动生成格式为 `username.nodename.基础域名` 的域名
3. 域名状态设置为 "pending"
4. 管理员可以激活域名，状态变为 "active"

#### 用户访问流程
1. 用户通过分配的域名访问服务
2. 系统通过域名查询对应的用户信息
3. 验证用户状态和权限
4. 允许或拒绝访问

## 数据模型

### Domain 模型
```go
type Domain struct {
    ID          uint         `json:"id"`
    Domain      string       `json:"domain"`      // 完整域名
    UserID      uint         `json:"user_id"`     // 用户ID
    User        User         `json:"user"`        // 关联用户
    NodeID      uint         `json:"node_id"`     // 节点ID
    Node        FrpsNode     `json:"node"`        // 关联节点
    Status      DomainStatus `json:"status"`      // 域名状态
    Description string       `json:"description"` // 描述
    IsActive    bool         `json:"is_active"`   // 是否激活
    CreatedAt   time.Time    `json:"created_at"`
    UpdatedAt   time.Time    `json:"updated_at"`
}
```

### 域名状态
- `active`: 活跃状态，可以正常使用
- `inactive`: 停用状态，暂时不可使用
- `pending`: 待激活状态，刚创建还未激活
- `error`: 错误状态，配置或验证失败

## 安全考虑

1. **域名唯一性**: 每个域名在系统中是唯一的
2. **用户权限**: 只有管理员可以管理域名
3. **状态控制**: 只有激活状态的域名才能被用于用户查询
4. **数据验证**: 创建域名时会验证用户和节点是否存在

## 扩展功能

### 可以进一步扩展的功能：
1. **域名解析**: 集成DNS管理，自动创建DNS记录
2. **SSL证书**: 自动申请和管理SSL证书
3. **域名监控**: 监控域名的可用性和响应时间
4. **批量操作**: 支持批量创建、激活、停用域名
5. **域名模板**: 支持自定义域名格式模板
6. **域名历史**: 记录域名的变更历史

## 故障排除

### 常见问题
1. **域名创建失败**: 检查用户和节点是否存在且有效
2. **域名冲突**: 确保用户名和节点名的组合是唯一的
3. **查询失败**: 确保域名状态为 active 且 is_active 为 true

### 日志查看
系统会记录域名相关的操作日志，可以通过后端日志查看详细信息。
