import { frpsAPI } from "@/utils/api";
import { TCPProxy } from "@/utils/proxy";
import { message } from "antd";
import React, { useEffect, useState } from "react";
import ProxyTable from "../components/ProxyTable";

interface ProxiesTCPProps {
  nodeId?: number;
}

const ProxiesTCP: React.FC<ProxiesTCPProps> = ({ nodeId }) => {
  const [loading, setLoading] = useState(false);
  const [proxies, setProxies] = useState<any[]>([]);

  useEffect(() => {
    if (nodeId) {
      fetchData();
    } else {
      setProxies([]);
    }
  }, [nodeId]);

  const fetchData = async () => {
    if (!nodeId) return;

    setLoading(true);
    try {
      const proxyResponse = await frpsAPI.getProxies("tcp", nodeId);
      const proxyData = proxyResponse.data;

      if (proxyData?.proxies) {
        const tcpProxies = proxyData.proxies.map(
          (proxy: any) => new TCPProxy(proxy)
        );
        setProxies(tcpProxies);
      } else {
        setProxies([]);
      }
    } catch (error) {
      message.error("获取TCP代理数据失败");
      console.error("Error fetching TCP proxies:", error);
      setProxies([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <ProxyTable
        proxies={proxies}
        proxyType="tcp"
        loading={loading}
        onRefresh={fetchData}
      />
    </div>
  );
};

export default ProxiesTCP;
