package controller

import (
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strconv"
	"strings"

	"frp-panel/pkg/model"
	"frp-panel/pkg/service"

	"github.com/gin-gonic/gin"
)

type FrpsNodeController struct {
	nodeService *service.FrpsNodeService
}

func NewFrpsNodeController(nodeService *service.FrpsNodeService) *FrpsNodeController {
	return &FrpsNodeController{
		nodeService: nodeService,
	}
}

// Register registers all frps node routes
func (c *FrpsNodeController) Register(engine *gin.Engine) {
	nodeGroup := engine.Group("/admin/nodes")
	{
		nodeGroup.GET("", MakeGinHandlerFunc(c.HandleGetNodes))
		nodeGroup.POST("", MakeGinHandlerFunc(c.HandleCreateNode))
		nodeGroup.GET("/:id", MakeGinHandlerFunc(c.HandleGetNode))
		nodeGroup.PUT("/:id", MakeGinHandlerFunc(c.<PERSON>le<PERSON>p<PERSON>))
		nodeGroup.DELETE("/:id", MakeGin<PERSON>andlerFunc(c.HandleDeleteNode))
		nodeGroup.POST("/:id/check", MakeGinHandlerFunc(c.HandleCheckNodeStatus))
		nodeGroup.POST("/:id/toggle", MakeGinHandlerFunc(c.HandleToggleNodeStatus))
		nodeGroup.GET("/:id/stats", MakeGinHandlerFunc(c.HandleGetNodeStats))
		nodeGroup.POST("/check-all", MakeGinHandlerFunc(c.HandleCheckAllNodes))
		nodeGroup.GET("/dashboard", MakeGinHandlerFunc(c.HandleGetDashboardStats))

		// Node-specific frps proxy routes
		nodeGroup.Any("/:id/frps/*path", c.HandleNodeFrpsProxy)
	}
}

// HandleGetNodes returns all frps nodes
func (c *FrpsNodeController) HandleGetNodes(ctx *gin.Context) (interface{}, error) {
	nodes, err := c.nodeService.GetAllNodes()
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	return nodes, nil
}

// HandleCreateNode creates a new frps node
func (c *FrpsNodeController) HandleCreateNode(ctx *gin.Context) (interface{}, error) {
	var req struct {
		Name        string                `json:"name" binding:"required"`
		Description string                `json:"description"`
		Host        string                `json:"host" binding:"required"`
		Port        int                   `json:"port" binding:"required"`
		AdminPort   int                   `json:"admin_port" binding:"required"`
		AdminUser   string                `json:"admin_user"`
		AdminPwd    string                `json:"admin_pwd"`
		Config      *model.FrpsNodeConfig `json:"config"`
		IsActive    *bool                 `json:"is_active"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	node := &model.FrpsNode{
		Name:        req.Name,
		Description: req.Description,
		Host:        req.Host,
		Port:        req.Port,
		AdminPort:   req.AdminPort,
		AdminUser:   req.AdminUser,
		AdminPwd:    req.AdminPwd,
		IsActive:    true,
	}

	if req.IsActive != nil {
		node.IsActive = *req.IsActive
	}

	if req.Config != nil {
		if err := node.SetConfig(req.Config); err != nil {
			return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
		}
	}

	if err := c.nodeService.CreateNode(node); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return node, nil
}

// HandleGetNode returns a specific frps node
func (c *FrpsNodeController) HandleGetNode(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	node, err := c.nodeService.GetNodeByID(uint(id))
	if err != nil {
		return nil, &HTTPError{Code: http.StatusNotFound, Err: err}
	}

	return node, nil
}

// HandleUpdateNode updates an existing frps node
func (c *FrpsNodeController) HandleUpdateNode(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	var req struct {
		Name        string                `json:"name"`
		Description string                `json:"description"`
		Host        string                `json:"host"`
		Port        int                   `json:"port"`
		AdminPort   int                   `json:"admin_port"`
		AdminUser   string                `json:"admin_user"`
		AdminPwd    string                `json:"admin_pwd"`
		Config      *model.FrpsNodeConfig `json:"config"`
		IsActive    *bool                 `json:"is_active"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	// Get existing node
	node, err := c.nodeService.GetNodeByID(uint(id))
	if err != nil {
		return nil, &HTTPError{Code: http.StatusNotFound, Err: err}
	}

	// Update fields
	if req.Name != "" {
		node.Name = req.Name
	}
	if req.Description != "" {
		node.Description = req.Description
	}
	if req.Host != "" {
		node.Host = req.Host
	}
	if req.Port != 0 {
		node.Port = req.Port
	}
	if req.AdminPort != 0 {
		node.AdminPort = req.AdminPort
	}
	if req.AdminUser != "" {
		node.AdminUser = req.AdminUser
	}
	if req.AdminPwd != "" {
		node.AdminPwd = req.AdminPwd
	}
	if req.IsActive != nil {
		node.IsActive = *req.IsActive
	}
	if req.Config != nil {
		if err := node.SetConfig(req.Config); err != nil {
			return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
		}
	}

	if err := c.nodeService.UpdateNode(node); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return node, nil
}

// HandleDeleteNode deletes a frps node
func (c *FrpsNodeController) HandleDeleteNode(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	if err := c.nodeService.DeleteNode(uint(id)); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return map[string]string{"message": "Node deleted successfully"}, nil
}

// HandleCheckNodeStatus checks the status of a specific node
func (c *FrpsNodeController) HandleCheckNodeStatus(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	node, err := c.nodeService.GetNodeByID(uint(id))
	if err != nil {
		return nil, &HTTPError{Code: http.StatusNotFound, Err: err}
	}

	if err := c.nodeService.CheckNodeStatus(node); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return node, nil
}

// HandleToggleNodeStatus toggles the active status of a node
func (c *FrpsNodeController) HandleToggleNodeStatus(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	if err := c.nodeService.ToggleNodeStatus(uint(id)); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	// Return updated node
	node, err := c.nodeService.GetNodeByID(uint(id))
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return node, nil
}

// HandleGetNodeStats returns statistics for a specific node
func (c *FrpsNodeController) HandleGetNodeStats(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	daysStr := ctx.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil {
		days = 30
	}

	stats, err := c.nodeService.GetNodeStats(uint(id), days)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return stats, nil
}

// HandleCheckAllNodes checks the status of all active nodes
func (c *FrpsNodeController) HandleCheckAllNodes(ctx *gin.Context) (interface{}, error) {
	if err := c.nodeService.CheckAllNodesStatus(); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return map[string]string{"message": "All nodes status checked"}, nil
}

// HandleGetDashboardStats returns dashboard statistics for all nodes
func (c *FrpsNodeController) HandleGetDashboardStats(ctx *gin.Context) (interface{}, error) {
	stats, err := c.nodeService.GetNodeDashboardStats()
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return stats, nil
}

// HandleNodeFrpsProxy proxies requests to a specific node's frps admin API
func (c *FrpsNodeController) HandleNodeFrpsProxy(ctx *gin.Context) {
	// Get node ID from URL parameter
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid node ID"})
		return
	}

	// Get the node information
	node, err := c.nodeService.GetNodeByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Node not found"})
		return
	}

	// Check if node is active and online
	if !node.IsActive {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{"error": "Node is not active"})
		return
	}

	if node.Status != model.NodeStatusOnline {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{"error": "Node is not online"})
		return
	}

	// Build target URL for the node's admin API
	targetURL, err := url.Parse(fmt.Sprintf("http://%s:%d", node.Host, node.AdminPort))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid node configuration"})
		return
	}

	// Create reverse proxy
	proxy := httputil.NewSingleHostReverseProxy(targetURL)

	// Modify the request
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)

		// Get the path after /admin/nodes/{id}/frps/
		path := ctx.Param("path")
		path = strings.TrimPrefix(path, "/")

		// Set the target path to /api/{path}
		req.URL.Path = "/api/" + path
		req.URL.RawQuery = ctx.Request.URL.RawQuery

		// Add basic auth if configured
		if node.AdminUser != "" && node.AdminPwd != "" {
			req.SetBasicAuth(node.AdminUser, node.AdminPwd)
		}
	}

	// Handle errors
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		ctx.JSON(http.StatusBadGateway, gin.H{
			"error":   "Failed to connect to node",
			"details": err.Error(),
		})
	}

	// Serve the request
	proxy.ServeHTTP(ctx.Writer, ctx.Request)
}
