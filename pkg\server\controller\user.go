package controller

import (
	"net/http"
	"strconv"

	"frp-panel/pkg/model"
	"frp-panel/pkg/service"

	"github.com/gin-gonic/gin"
)

type UserController struct {
	authService      *service.AuthService
	userService      *service.UserService
	trafficService   *service.TrafficService
	subscriptionCtrl *SubscriptionController
}

func NewUserController(authService *service.AuthService, userService *service.UserService, trafficService *service.TrafficService) *UserController {
	return &UserController{
		authService:      authService,
		userService:      userService,
		trafficService:   trafficService,
		subscriptionCtrl: NewSubscriptionController(userService),
	}
}

func (c *UserController) Register(engine *gin.Engine) {
	api := engine.Group("/api")
	{
		// Public routes
		api.POST("/register", MakeGinHandlerFunc(c.HandleRegister))
		api.POST("/login", MakeGinHandlerFunc(c.<PERSON>le<PERSON>og<PERSON>))

		// Protected routes
		protected := api.Group("/")
		protected.Use(c.AuthMiddleware())
		{
			protected.GET("/profile", MakeGinHandlerFunc(c.HandleGetProfile))
			protected.PUT("/profile", MakeGinHandlerFunc(c.HandleUpdateProfile))
			protected.GET("/stats", MakeGinHandlerFunc(c.HandleGetStats))
			protected.GET("/traffic", MakeGinHandlerFunc(c.HandleGetTraffic))
			// Subscription management
			protected.POST("/subscriptions/purchase", MakeGinHandlerFunc(c.subscriptionCtrl.PurchaseService))
			protected.GET("/subscriptions", MakeGinHandlerFunc(c.subscriptionCtrl.GetUserSubscriptions))

		}
	}
}

// AuthMiddleware validates JWT token
func (c *UserController) AuthMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		token := ctx.GetHeader("Authorization")
		if token == "" {
			ctx.JSON(http.StatusUnauthorized, &Response{Msg: "Authorization header required"})
			ctx.Abort()
			return
		}

		// Remove "Bearer " prefix if present
		if len(token) > 7 && token[:7] == "Bearer " {
			token = token[7:]
		}

		user, err := c.authService.GetUserByToken(token)
		if err != nil {
			ctx.JSON(http.StatusUnauthorized, &Response{Msg: "Invalid token"})
			ctx.Abort()
			return
		}

		ctx.Set("user", user)
		ctx.Next()
	}
}

func (c *UserController) HandleRegister(ctx *gin.Context) (interface{}, error) {
	var req struct {
		Username string `json:"username" binding:"required"`
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required,min=6"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	user, err := c.authService.Register(req.Username, req.Email, req.Password)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	return map[string]interface{}{
		"message": "User registered successfully",
		"user":    user,
		"token":   user.Token,
	}, nil
}

func (c *UserController) HandleLogin(ctx *gin.Context) (interface{}, error) {
	var req struct {
		Email    string `json:"email" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	user, err := c.authService.LoginWithEmail(req.Email, req.Password)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusUnauthorized,
			Err:  err,
		}
	}

	return map[string]interface{}{
		"message": "Login successful",
		"user":    user,
		"token":   user.Token,
	}, nil
}

func (c *UserController) HandleGetProfile(ctx *gin.Context) (interface{}, error) {
	user := ctx.MustGet("user").(*model.User)
	return user, nil
}

func (c *UserController) HandleUpdateProfile(ctx *gin.Context) (interface{}, error) {
	user := ctx.MustGet("user").(*model.User)

	var req struct {
		Username string `json:"username"`
		Email    string `json:"email"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	if req.Username != "" {
		user.Username = req.Username
	}
	if req.Email != "" {
		user.Email = req.Email
	}

	if err := c.userService.UpdateUser(user); err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return map[string]interface{}{
		"message": "Profile updated successfully",
		"user":    user,
	}, nil
}

func (c *UserController) HandleGetStats(ctx *gin.Context) (interface{}, error) {
	user := ctx.MustGet("user").(*model.User)

	stats, err := c.userService.GetUserStats(user.ID)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return stats, nil
}

func (c *UserController) HandleGetTraffic(ctx *gin.Context) (interface{}, error) {
	user := ctx.MustGet("user").(*model.User)

	daysStr := ctx.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil {
		days = 30
	}

	chartData, err := c.trafficService.GetDailyTrafficChart(days)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return map[string]interface{}{
		"chart_data": chartData,
		"user_id":    user.ID,
	}, nil
}
