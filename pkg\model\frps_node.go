package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// FrpsNodeStatus represents the status of a frps node
type FrpsNodeStatus string

const (
	NodeStatusOnline  FrpsNodeStatus = "online"
	NodeStatusOffline FrpsNodeStatus = "offline"
	NodeStatusError   FrpsNodeStatus = "error"
	NodeStatusUnknown FrpsNodeStatus = "unknown"
)

// FrpsNodeConfig represents the configuration for a frps node
type FrpsNodeConfig struct {
	BindPort                int               `json:"bind_port"`
	VhostHTTPPort           int               `json:"vhost_http_port,omitempty"`
	VhostHTTPSPort          int               `json:"vhost_https_port,omitempty"`
	DashboardPort           int               `json:"dashboard_port,omitempty"`
	DashboardUser           string            `json:"dashboard_user,omitempty"`
	DashboardPwd            string            `json:"dashboard_pwd,omitempty"`
	Token                   string            `json:"token,omitempty"`
	MaxPoolCount            int               `json:"max_pool_count,omitempty"`
	LogLevel                string            `json:"log_level,omitempty"`
	LogMaxDays              int               `json:"log_max_days,omitempty"`
	DisableLogColor         bool              `json:"disable_log_color,omitempty"`
	DetailedErrorsToClient  bool              `json:"detailed_errors_to_client,omitempty"`
	Authentication          map[string]string `json:"authentication,omitempty"`
	HeartbeatTimeout        int               `json:"heartbeat_timeout,omitempty"`
	UserConnTimeout         int               `json:"user_conn_timeout,omitempty"`
	UDPPacketSize           int               `json:"udp_packet_size,omitempty"`
	AllowPorts              string            `json:"allow_ports,omitempty"`
	MaxPortsPerClient       int               `json:"max_ports_per_client,omitempty"`
	TLSOnly                 bool              `json:"tls_only,omitempty"`
	SubdomainHost           string            `json:"subdomain_host,omitempty"`
	TCPMux                  bool              `json:"tcp_mux,omitempty"`
	TCPMuxKeepaliveInterval int               `json:"tcp_mux_keepalive_interval,omitempty"`
	TCPKeepalive            int               `json:"tcp_keepalive,omitempty"`
	CustomDomains           []string          `json:"custom_domains,omitempty"`
}

// FrpsNode represents a frps server node
type FrpsNode struct {
	ID          uint           `gorm:"primarykey" json:"id"`
	Name        string         `gorm:"uniqueIndex;not null;size:100;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"name"`
	Description string         `gorm:"type:text;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"description"`
	Host        string         `gorm:"not null;size:255" json:"host"`
	Port        int            `gorm:"not null" json:"port"`
	AdminPort   int            `gorm:"not null" json:"admin_port"`
	AdminUser   string         `gorm:"size:100" json:"admin_user"`
	AdminPwd    string         `gorm:"size:100" json:"admin_pwd"`
	Status      FrpsNodeStatus `gorm:"default:'unknown';size:20" json:"status"`
	Config      string         `gorm:"type:longtext;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"config"` // JSON string of FrpsNodeConfig
	IsActive    bool           `gorm:"default:true" json:"is_active"`
	LastCheck   *time.Time     `json:"last_check"`
	ErrorMsg    string         `gorm:"type:text;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"error_msg"`
	Version     string         `gorm:"size:50" json:"version"`
	Uptime      int64          `gorm:"default:0" json:"uptime"` // seconds
	ClientCount int            `gorm:"default:0" json:"client_count"`
	ProxyCount  int            `gorm:"default:0" json:"proxy_count"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

// GetConfig parses the config JSON string and returns FrpsNodeConfig
func (n *FrpsNode) GetConfig() (*FrpsNodeConfig, error) {
	if n.Config == "" {
		return &FrpsNodeConfig{}, nil
	}

	var config FrpsNodeConfig
	err := json.Unmarshal([]byte(n.Config), &config)
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// SetConfig sets the config from FrpsNodeConfig struct
func (n *FrpsNode) SetConfig(config *FrpsNodeConfig) error {
	if config == nil {
		n.Config = ""
		return nil
	}

	configBytes, err := json.Marshal(config)
	if err != nil {
		return err
	}
	n.Config = string(configBytes)
	return nil
}

// GetAdminURL returns the admin URL for this node
func (n *FrpsNode) GetAdminURL() string {
	return "http://" + n.Host + ":" + string(rune(n.AdminPort))
}

// IsOnline checks if the node is currently online
func (n *FrpsNode) IsOnline() bool {
	return n.Status == NodeStatusOnline
}

// UpdateStatus updates the node status and last check time
func (n *FrpsNode) UpdateStatus(status FrpsNodeStatus, errorMsg string) {
	n.Status = status
	n.ErrorMsg = errorMsg
	now := time.Now()
	n.LastCheck = &now
}

// FrpsNodeStats represents statistics for a frps node
type FrpsNodeStats struct {
	ID              uint      `gorm:"primarykey" json:"id"`
	NodeID          uint      `gorm:"not null;index" json:"node_id"`
	Node            FrpsNode  `gorm:"foreignKey:NodeID" json:"node,omitempty"`
	ClientCount     int       `gorm:"default:0" json:"client_count"`
	ProxyCount      int       `gorm:"default:0" json:"proxy_count"`
	TotalTrafficIn  int64     `gorm:"default:0" json:"total_traffic_in"`
	TotalTrafficOut int64     `gorm:"default:0" json:"total_traffic_out"`
	CurConns        int       `gorm:"default:0" json:"cur_conns"`
	Uptime          int64     `gorm:"default:0" json:"uptime"`
	RecordedAt      time.Time `gorm:"not null;index" json:"recorded_at"`
	CreatedAt       time.Time `json:"created_at"`
}
