import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Descriptions, message, Spin } from 'antd';
import { Pie } from '@ant-design/plots';
import { frpsAPI } from '@/utils/api';

interface ServerInfo {
  version: string;
  bindPort: number;
  kcpBindPort: number;
  quicBindPort: number;
  vhostHTTPPort: number;
  vhostHTTPSPort: number;
  tcpmuxHTTPConnectPort: number;
  subdomainHost: string;
  maxPoolCount: number;
  maxPortsPerClient: string;
  allowPortsStr: string;
  tlsForce: boolean;
  heartbeatTimeout: number;
  clientCounts: number;
  curConns: number;
  totalTrafficIn: number;
  totalTrafficOut: number;
  proxyTypeCount: {
    tcp?: number;
    udp?: number;
    http?: number;
    https?: number;
    stcp?: number;
    sudp?: number;
    xtcp?: number;
    tcpmux?: number;
  };
}

interface FrpsNode {
  id: number;
  name: string;
  description: string;
  host: string;
  port: number;
  admin_port: number;
  status: 'online' | 'offline' | 'error' | 'unknown';
  is_active: boolean;
}

interface NodeOverviewProps {
  nodeId: number;
  node: FrpsNode;
  refreshKey?: number;
}

const NodeOverview: React.FC<NodeOverviewProps> = ({ nodeId, node, refreshKey }) => {
  const [loading, setLoading] = useState(false);
  const [serverInfo, setServerInfo] = useState<ServerInfo | null>(null);

  useEffect(() => {
    if (nodeId) {
      loadServerInfo(nodeId);
    }
  }, [nodeId, refreshKey]);

  const loadServerInfo = async (nodeId: number) => {
    setLoading(true);
    try {
      const response = await frpsAPI.getServerInfo(nodeId);
      const data = response.data;
      
      setServerInfo({
        ...data,
        maxPortsPerClient: data.maxPortsPerClient === '0' ? 'no limit' : data.maxPortsPerClient,
      });
    } catch (error) {
      message.error('获取服务器信息失败！');
      console.error('Error loading server info:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getTrafficData = () => {
    if (!serverInfo) return [];
    return [
      {
        type: 'Traffic In',
        value: serverInfo.totalTrafficIn,
        formatted: formatBytes(serverInfo.totalTrafficIn),
      },
      {
        type: 'Traffic Out',
        value: serverInfo.totalTrafficOut,
        formatted: formatBytes(serverInfo.totalTrafficOut),
      },
    ];
  };

  const getProxyData = (): Array<{type: string; value: number}> => {
    if (!serverInfo?.proxyTypeCount) return [];
    const data: Array<{type: string; value: number}> = [];
    
    Object.entries(serverInfo.proxyTypeCount).forEach(([type, count]) => {
      if (count && count > 0) {
        data.push({
          type: type.toUpperCase(),
          value: count,
        });
      }
    });
    
    return data;
  };

  const trafficConfig = {
    appendPadding: 10,
    data: getTrafficData(),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      content: ({ value }: { value: number }) => formatBytes(value),
      style: {
        fontSize: 14,
        textAlign: 'center',
      },
    },
    interactions: [{ type: 'element-active' }],
  };

  const proxyConfig = {
    appendPadding: 10,
    data: getProxyData(),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      content: ({ value }: { value: number }) => value,
      style: {
        fontSize: 14,
        textAlign: 'center',
      },
    },
    interactions: [{ type: 'element-active' }],
  };

  return (
    <Spin spinning={loading}>
      <Row gutter={[16, 16]}>
        {/* 服务器信息 */}
        <Col xs={24} lg={12}>
          <Card title="服务器信息" style={{ height: '100%' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="版本">{serverInfo?.version}</Descriptions.Item>
              <Descriptions.Item label="绑定端口">{serverInfo?.bindPort}</Descriptions.Item>
              {serverInfo?.kcpBindPort !== 0 && (
                <Descriptions.Item label="KCP 绑定端口">{serverInfo?.kcpBindPort}</Descriptions.Item>
              )}
              {serverInfo?.quicBindPort !== 0 && (
                <Descriptions.Item label="QUIC 绑定端口">{serverInfo?.quicBindPort}</Descriptions.Item>
              )}
              {serverInfo?.vhostHTTPPort !== 0 && (
                <Descriptions.Item label="HTTP 端口">{serverInfo?.vhostHTTPPort}</Descriptions.Item>
              )}
              {serverInfo?.vhostHTTPSPort !== 0 && (
                <Descriptions.Item label="HTTPS 端口">{serverInfo?.vhostHTTPSPort}</Descriptions.Item>
              )}
              {serverInfo?.tcpmuxHTTPConnectPort !== 0 && (
                <Descriptions.Item label="TCPMux HTTPConnect 端口">{serverInfo?.tcpmuxHTTPConnectPort}</Descriptions.Item>
              )}
              {serverInfo?.subdomainHost && (
                <Descriptions.Item label="子域名主机">{serverInfo?.subdomainHost}</Descriptions.Item>
              )}
              <Descriptions.Item label="最大连接池数量">{serverInfo?.maxPoolCount}</Descriptions.Item>
              <Descriptions.Item label="每客户端最大端口数">{serverInfo?.maxPortsPerClient}</Descriptions.Item>
              {serverInfo?.allowPortsStr && (
                <Descriptions.Item label="允许端口">{serverInfo?.allowPortsStr}</Descriptions.Item>
              )}
              {serverInfo?.tlsForce && (
                <Descriptions.Item label="强制 TLS">{serverInfo?.tlsForce ? '是' : '否'}</Descriptions.Item>
              )}
              <Descriptions.Item label="心跳超时">{serverInfo?.heartbeatTimeout}s</Descriptions.Item>
              <Descriptions.Item label="客户端数量">{serverInfo?.clientCounts}</Descriptions.Item>
              <Descriptions.Item label="当前连接数">{serverInfo?.curConns}</Descriptions.Item>
              <Descriptions.Item label="代理数量">
                {serverInfo ? Object.values(serverInfo.proxyTypeCount || {}).reduce((a, b) => (a || 0) + (b || 0), 0) : 0}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 图表区域 */}
        <Col xs={24} lg={12}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="网络流量" style={{ height: 300 }}>
                {getTrafficData().length > 0 ? (
                  <Pie {...trafficConfig} height={200} />
                ) : (
                  <div style={{ textAlign: 'center', padding: '60px 0' }}>暂无流量数据</div>
                )}
              </Card>
            </Col>
            <Col span={24}>
              <Card title="代理分布" style={{ height: 300 }}>
                {getProxyData().length > 0 ? (
                  <Pie {...proxyConfig} height={200} />
                ) : (
                  <div style={{ textAlign: 'center', padding: '60px 0' }}>暂无代理数据</div>
                )}
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
    </Spin>
  );
};

export default NodeOverview;
