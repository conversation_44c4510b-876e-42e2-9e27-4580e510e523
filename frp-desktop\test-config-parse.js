#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🧪 测试配置文件解析...\n')

// 读取配置文件
const configPath = path.join(__dirname, '../conf/frpc.toml')
console.log(`📁 配置文件路径: ${configPath}`)

if (!fs.existsSync(configPath)) {
  console.log('❌ 配置文件不存在!')
  process.exit(1)
}

const content = fs.readFileSync(configPath, 'utf8')
console.log('📄 配置文件内容:')
console.log('─'.repeat(50))
console.log(content)
console.log('─'.repeat(50))

// 简单解析测试
function parseTomlConfig(content) {
  const config = {
    serverAddr: '127.0.0.1',
    serverPort: 7000,
    token: '',
    user: '',
    metadatas: {},
    proxies: []
  }

  const lines = content.split('\n')
  let currentProxy = null
  let inProxySection = false

  for (const line of lines) {
    const trimmed = line.trim()
    if (!trimmed || trimmed.startsWith('#')) continue

    // Handle [[proxies]] sections
    if (trimmed === '[[proxies]]') {
      if (currentProxy && currentProxy.name) {
        config.proxies.push(currentProxy)
      }
      currentProxy = {}
      inProxySection = true
      continue
    }

    // Handle other sections
    if (trimmed.startsWith('[') && trimmed.endsWith(']') && trimmed !== '[[proxies]]') {
      if (inProxySection && currentProxy && currentProxy.name) {
        config.proxies.push(currentProxy)
        currentProxy = null
      }
      inProxySection = false
      continue
    }

    // Key-value pairs
    const equalIndex = trimmed.indexOf('=')
    if (equalIndex === -1) continue

    const key = trimmed.slice(0, equalIndex).trim()
    let value = trimmed.slice(equalIndex + 1).trim()

    // Remove quotes and parse arrays
    if (value.startsWith('"') && value.endsWith('"')) {
      value = value.slice(1, -1)
    } else if (value.startsWith('[') && value.endsWith(']')) {
      const arrayContent = value.slice(1, -1)
      const arrayValues = arrayContent.split(',').map(v => v.trim().replace(/"/g, ''))
      
      if (inProxySection && currentProxy) {
        if (key === 'customDomains') {
          currentProxy.customDomains = arrayValues
        }
      }
      continue
    }

    if (inProxySection && currentProxy) {
      // Proxy configuration
      switch (key) {
        case 'name':
          currentProxy.name = value
          break
        case 'type':
          currentProxy.type = value
          break
        case 'localIP':
          currentProxy.localIP = value
          break
        case 'localPort':
          currentProxy.localPort = parseInt(value)
          break
        case 'remotePort':
          currentProxy.remotePort = parseInt(value)
          break
        default:
          if (!key.startsWith('metadatas.')) {
            currentProxy[key] = value
          }
      }
    } else {
      // Global configuration
      switch (key) {
        case 'serverAddr':
          config.serverAddr = value
          break
        case 'serverPort':
          config.serverPort = parseInt(value)
          break
        default:
          if (key.startsWith('metadatas.')) {
            const metaKey = key.slice(10)
            config.metadatas[metaKey] = value
            
            if (metaKey === 'token') {
              config.token = value
            } else if (metaKey === 'username') {
              config.user = value
            }
          }
      }
    }
  }

  // Save last proxy
  if (inProxySection && currentProxy && currentProxy.name) {
    config.proxies.push(currentProxy)
  }

  return config
}

try {
  const parsedConfig = parseTomlConfig(content)
  
  console.log('\n✅ 解析结果:')
  console.log('─'.repeat(50))
  console.log(`服务器地址: ${parsedConfig.serverAddr}`)
  console.log(`服务器端口: ${parsedConfig.serverPort}`)
  console.log(`用户名: ${parsedConfig.user}`)
  console.log(`Token: ${parsedConfig.token ? parsedConfig.token.substring(0, 20) + '...' : '未设置'}`)
  console.log(`隧道数量: ${parsedConfig.proxies.length}`)
  
  console.log('\n📋 隧道列表:')
  parsedConfig.proxies.forEach((proxy, index) => {
    console.log(`  ${index + 1}. ${proxy.name} (${proxy.type}) - ${proxy.localIP}:${proxy.localPort}`)
    if (proxy.remotePort) {
      console.log(`     远程端口: ${proxy.remotePort}`)
    }
    if (proxy.customDomains) {
      console.log(`     自定义域名: ${proxy.customDomains.join(', ')}`)
    }
  })
  
  console.log('\n🎉 配置文件解析成功!')
  
} catch (error) {
  console.log(`❌ 解析失败: ${error.message}`)
  process.exit(1)
}
