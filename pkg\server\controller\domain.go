package controller

import (
	"errors"
	"net/http"
	"strconv"

	"frp-panel/pkg/service"

	"github.com/gin-gonic/gin"
)

type DomainController struct {
	domainService *service.DomainService
}

func NewDomainController(domainService *service.DomainService) *DomainController {
	return &DomainController{
		domainService: domainService,
	}
}

// Register registers all domain routes
func (c *DomainController) Register(engine *gin.Engine) {
	domainGroup := engine.Group("/admin/domains")
	{
		domainGroup.GET("", MakeGinHandlerFunc(c.HandleGetDomains))
		domainGroup.POST("", MakeGin<PERSON>andlerFunc(c.HandleCreateDomain))
		domainGroup.GET("/:id", MakeGinHandlerFunc(c.HandleGetDomain))
		domainGroup.PUT("/:id", MakeGinHandlerFunc(c.HandleUpdateDomain))
		domainGroup.DELETE("/:id", MakeGinHandlerFunc(c.<PERSON>teDomain))
		domainGroup.POST("/:id/activate", MakeGin<PERSON><PERSON>ler<PERSON>unc(c.HandleActivateDomain))
		domainGroup.POST("/:id/deactivate", MakeGinHandlerFunc(c.HandleDeactivateDomain))
		domainGroup.GET("/stats", MakeGinHandlerFunc(c.HandleGetDomainStats))
		domainGroup.POST("/check-conflict", MakeGinHandlerFunc(c.HandleCheckDomainConflict))
	}

	// Public API for domain lookup
	publicGroup := engine.Group("/api/domains")
	{
		publicGroup.GET("/lookup/:domain", MakeGinHandlerFunc(c.HandleLookupUserByDomain))
	}
}

// HandleGetDomains returns all domains
func (c *DomainController) HandleGetDomains(ctx *gin.Context) (interface{}, error) {
	domains, err := c.domainService.GetAllDomains()
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	return domains, nil
}

// HandleCreateDomain creates a new domain
func (c *DomainController) HandleCreateDomain(ctx *gin.Context) (interface{}, error) {
	var req struct {
		UserID      uint   `json:"user_id" binding:"required"`
		NodeID      uint   `json:"node_id" binding:"required"`
		Description string `json:"description"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	domain, err := c.domainService.CreateDomain(req.UserID, req.NodeID, req.Description)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	return domain, nil
}

// HandleGetDomain returns a specific domain
func (c *DomainController) HandleGetDomain(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	domain, err := c.domainService.GetDomainByID(uint(id))
	if err != nil {
		return nil, &HTTPError{Code: http.StatusNotFound, Err: err}
	}

	return domain, nil
}

// HandleUpdateDomain updates a domain
func (c *DomainController) HandleUpdateDomain(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	var req struct {
		Status      string `json:"status"`
		Description string `json:"description"`
		IsActive    *bool  `json:"is_active"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	updates := make(map[string]interface{})
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	domain, err := c.domainService.UpdateDomain(uint(id), updates)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	return domain, nil
}

// HandleDeleteDomain deletes a domain
func (c *DomainController) HandleDeleteDomain(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	if err := c.domainService.DeleteDomain(uint(id)); err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return gin.H{"message": "Domain deleted successfully"}, nil
}

// HandleActivateDomain activates a domain
func (c *DomainController) HandleActivateDomain(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	domain, err := c.domainService.ActivateDomain(uint(id))
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return domain, nil
}

// HandleDeactivateDomain deactivates a domain
func (c *DomainController) HandleDeactivateDomain(ctx *gin.Context) (interface{}, error) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	domain, err := c.domainService.DeactivateDomain(uint(id))
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}

	return domain, nil
}

// HandleGetDomainStats returns domain statistics
func (c *DomainController) HandleGetDomainStats(ctx *gin.Context) (interface{}, error) {
	stats, err := c.domainService.GetDomainStats()
	if err != nil {
		return nil, &HTTPError{Code: http.StatusInternalServerError, Err: err}
	}
	return stats, nil
}

// HandleCheckDomainConflict checks if a domain would conflict
func (c *DomainController) HandleCheckDomainConflict(ctx *gin.Context) (interface{}, error) {
	var req struct {
		UserID uint `json:"user_id" binding:"required"`
		NodeID uint `json:"node_id" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	conflict, domain, err := c.domainService.CheckDomainConflict(req.UserID, req.NodeID)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: err}
	}

	return gin.H{
		"conflict": conflict,
		"domain":   domain,
	}, nil
}

// HandleLookupUserByDomain looks up user by domain name
func (c *DomainController) HandleLookupUserByDomain(ctx *gin.Context) (interface{}, error) {
	domainName := ctx.Param("domain")
	if domainName == "" {
		return nil, &HTTPError{Code: http.StatusBadRequest, Err: gin.Error{Err: errors.New("domain parameter is required")}}
	}

	user, err := c.domainService.GetUserByDomain(domainName)
	if err != nil {
		return nil, &HTTPError{Code: http.StatusNotFound, Err: err}
	}

	// Return limited user information for security
	return gin.H{
		"id":       user.ID,
		"username": user.Username,
		"status":   user.Status,
		"role":     user.Role,
	}, nil
}
