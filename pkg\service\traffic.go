package service

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
)

type TrafficService struct{}

// enforceQuotaIfExceeded checks user's monthly usage against their service plan and deactivates proxies if exceeded.
func (s *TrafficService) enforceQuotaIfExceeded(userID uint) error {
	// Load user with service
	var user model.User
	if err := database.DB.Preload("Service").First(&user, userID).Error; err != nil {
		return err
	}
	if user.Service == nil || !user.Service.Active {
		// No active service -> treat as exceeded: deactivate proxies
		return database.DB.Model(&model.Proxy{}).Where("user_id = ?", userID).Update("active", false).Error
	}
	// Sum current month usage
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var totalBytes int64
	if err := database.DB.Model(&model.TrafficUsage{}).
		Where("user_id = ? AND date >= ?", userID, startOfMonth).
		Select("COALESCE(SUM(bytes),0)").Scan(&totalBytes).Error; err != nil {
		return err
	}
	limitBytes := user.Service.TrafficMB * 1024 * 1024
	if limitBytes <= 0 {
		// No allowance -> block
		return database.DB.Model(&model.Proxy{}).Where("user_id = ?", userID).Update("active", false).Error
	}
	if totalBytes >= limitBytes {
		// Deactivate all proxies for this user
		return database.DB.Model(&model.Proxy{}).Where("user_id = ?", userID).Update("active", false).Error
	}
	return nil
}

// ResetCurrentMonth deletes traffic usage records for the current month for a specific user.
func (s *TrafficService) ResetCurrentMonth(userID uint) error {
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	return database.DB.Where("user_id = ? AND date >= ?", userID, startOfMonth).
		Delete(&model.TrafficUsage{}).Error
}

func NewTrafficService() *TrafficService {
	return &TrafficService{}
}

// StartCollector periodically polls frps Admin API and aggregates per-user traffic deltas.
// It supports optional Basic Auth via username/password. baseAPI example: http://127.0.0.1:7500/api
func (s *TrafficService) StartCollector(baseAPI, username, password string, interval time.Duration, stopCh <-chan struct{}) {
	if interval <= 0 {
		interval = 60 * time.Second
	}

	log.Printf("Starting traffic collector: baseAPI=%s, interval=%v", baseAPI, interval)

	ticker := time.NewTicker(interval)
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				if err := s.collectOnce(baseAPI, username, password); err != nil {
					log.Printf("Traffic collection failed: %v", err)
				} else {
					log.Printf("Traffic collection completed successfully")
				}
			case <-stopCh:
				log.Printf("Traffic collector stopped")
				return
			}
		}
	}()
}

// collectOnce fetches proxies from frps admin API and records traffic deltas per user.
func (s *TrafficService) collectOnce(baseAPI, username, password string) error {
	// Try unified endpoint first
	urls := []string{
		strings.TrimRight(baseAPI, "/") + "/proxies",
	}
	// Fallback per-type endpoints (common across versions)
	types := []string{"tcp", "udp", "http", "https", "stcp", "sudp", "xtcp"}
	for _, t := range types {
		urls = append(urls, fmt.Sprintf("%s/proxy/%s", strings.TrimRight(baseAPI, "/"), t))
	}

	for _, u := range urls {
		if err := s.fetchAndAccumulate(u, username, password); err == nil {
			// Succeeded at least once; no need to try more endpoints this tick
			return nil
		}
	}
	return fmt.Errorf("all frps endpoints failed")
}

func (s *TrafficService) fetchAndAccumulate(url, username, password string) error {
	log.Printf("Fetching traffic data from: %s", url)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		log.Printf("Failed to create request for %s: %v", url, err)
		return err
	}
	if username != "" {
		req.SetBasicAuth(username, password)
		log.Printf("Using basic auth for %s", url)
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Printf("HTTP request failed for %s: %v", url, err)
		return err
	}
	defer resp.Body.Close()

	log.Printf("Response status for %s: %s", url, resp.Status)

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("Bad response from %s: %s, body: %s", url, resp.Status, string(body))
		return fmt.Errorf("bad status: %s", resp.Status)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read response body from %s: %v", url, err)
		return err
	}

	log.Printf("Response body from %s: %s", url, string(body))

	// Flexible decoding: response could be {"proxies": [...] } or directly []
	var wrapper struct {
		Proxies []map[string]interface{} `json:"proxies"`
	}
	if err := json.Unmarshal(body, &wrapper); err == nil && len(wrapper.Proxies) > 0 {
		return s.processProxies(wrapper.Proxies)
	}
	var arr []map[string]interface{}
	if err := json.Unmarshal(body, &arr); err == nil && len(arr) > 0 {
		return s.processProxies(arr)
	}
	// Unknown format
	return fmt.Errorf("unrecognized frps response format")
}

func (s *TrafficService) processProxies(items []map[string]interface{}) error {
	for _, it := range items {
		name := firstString(it, "name", "proxy_name", "ProxyName")
		if name == "" {
			continue
		}

		// Extract user information and status from proxy metadata
		username, _, status := extractProxyMetadata(it)

		// Extract cumulative bytes; try several common keys
		inBytes := firstBytes(it, "in_bytes", "traffic_in", "trafficIn", "today_traffic_in", "todayTrafficIn")
		outBytes := firstBytes(it, "out_bytes", "traffic_out", "trafficOut", "today_traffic_out", "todayTrafficOut")
		if inBytes < 0 && outBytes < 0 {
			continue
		}

		// Find proxy record in database
		var p model.Proxy
		if err := database.DB.Where("name = ?", name).First(&p).Error; err != nil {
			log.Printf("No proxy found for name '%s': %v", name, err)
			continue // no mapping to user; skip
		}

		// Verify user ownership if we have metadata
		if username != "" {
			var user model.User
			if err := database.DB.Where("id = ?", p.UserID).First(&user).Error; err != nil {
				log.Printf("Failed to find user for proxy '%s': %v", name, err)
				continue
			}
			if user.Username != username {
				log.Printf("Username mismatch for proxy '%s': expected '%s', got '%s'",
					name, user.Username, username)
				continue
			}
		}

		log.Printf("Found proxy: ID=%d, UserID=%d, Name=%s, Username=%s, Status=%s, LastInBytes=%d, LastOutBytes=%d",
			p.ID, p.UserID, p.Name, username, status, p.LastInBytes, p.LastOutBytes)

		// Default zero if missing
		if inBytes < 0 {
			inBytes = p.LastInBytes
		}
		if outBytes < 0 {
			outBytes = p.LastOutBytes
		}

		log.Printf("Traffic calculation for %s: current(in=%d, out=%d), last(in=%d, out=%d)",
			name, inBytes, outBytes, p.LastInBytes, p.LastOutBytes)

		din := inBytes - p.LastInBytes
		dout := outBytes - p.LastOutBytes
		if din < 0 {
			din = 0
		}
		if dout < 0 {
			dout = 0
		}
		delta := din + dout

		log.Printf("Traffic delta for %s: din=%d, dout=%d, total=%d", name, din, dout, delta)

		// Update last counters
		p.LastInBytes = inBytes
		p.LastOutBytes = outBytes
		if err := database.DB.Save(&p).Error; err != nil {
			log.Printf("Failed to update proxy %s counters: %v", name, err)
		}

		if delta > 0 {
			// Record per-user delta bytes
			if err := s.RecordTraffic(p.UserID, delta); err != nil {
				log.Printf("Failed to record traffic for user %d: %v", p.UserID, err)
			} else {
				log.Printf("Recorded %d bytes traffic for user %d (proxy: %s)", delta, p.UserID, name)
			}
			// Enforce quota after recording
			if err := s.enforceQuotaIfExceeded(p.UserID); err != nil {
				log.Printf("Failed to enforce quota for user %d: %v", p.UserID, err)
			}
		} else {
			log.Printf("No traffic delta to record for proxy %s", name)
		}
	}
	return nil
}

// extractProxyMetadata extracts metadata from frps proxy response
func extractProxyMetadata(proxyData map[string]interface{}) (username, token, status string) {
	// Extract status
	if s, ok := proxyData["status"].(string); ok {
		status = s
	}

	// Extract user information from conf.metadatas
	if conf, ok := proxyData["conf"].(map[string]interface{}); ok {
		if metadatas, ok := conf["metadatas"].(map[string]interface{}); ok {
			if u, ok := metadatas["username"].(string); ok {
				username = u
			}
			if t, ok := metadatas["token"].(string); ok {
				token = t
			}
		}
	}
	return
}

func firstString(m map[string]interface{}, keys ...string) string {
	for _, k := range keys {
		if v, ok := m[k]; ok {
			switch t := v.(type) {
			case string:
				return t
			}
		}
	}
	return ""
}

// firstBytes returns bytes value from various possible fields.
// Supports numeric (float64/json number) or human string like "1.2 GB" or "12345".
func firstBytes(m map[string]interface{}, keys ...string) int64 {
	for _, k := range keys {
		if v, ok := m[k]; ok {
			switch t := v.(type) {
			case float64:
				return int64(t)
			case int64:
				return t
			case int:
				return int64(t)
			case string:
				if b, ok := parseHumanBytes(t); ok {
					return b
				}
			}
		}
	}
	return -1
}

// parseHumanBytes parses strings like "12345", "12.3 KB", "4.5MB", "1.2 GB".
func parseHumanBytes(s string) (int64, bool) {
	s = strings.TrimSpace(strings.ToUpper(s))
	// pure number
	if allDigits(s) {
		var n int64
		_, err := fmt.Sscanf(s, "%d", &n)
		if err == nil {
			return n, true
		}
	}
	// with unit
	units := []struct {
		suf string
		mul int64
	}{
		{"B", 1}, {"KB", 1024}, {"MB", 1024 * 1024}, {"GB", 1024 * 1024 * 1024}, {"TB", 1024 * 1024 * 1024 * 1024},
	}
	// remove spaces
	s = strings.ReplaceAll(s, " ", "")
	for _, u := range units {
		if strings.HasSuffix(s, u.suf) {
			num := strings.TrimSuffix(s, u.suf)
			var f float64
			_, err := fmt.Sscanf(num, "%f", &f)
			if err == nil {
				return int64(f * float64(u.mul)), true
			}
		}
	}
	return 0, false
}

func allDigits(s string) bool {
	if s == "" {
		return false
	}
	for i := 0; i < len(s); i++ {
		if s[i] < '0' || s[i] > '9' {
			return false
		}
	}
	return true
}

// RecordTraffic records traffic usage for a user
func (s *TrafficService) RecordTraffic(userID uint, bytes int64) error {
	traffic := &model.TrafficUsage{
		UserID: userID,
		Bytes:  bytes,
		Date:   time.Now(),
	}
	return database.DB.Create(traffic).Error
}

// GetUserTrafficUsage returns traffic usage for a user within a date range
func (s *TrafficService) GetUserTrafficUsage(userID uint, startDate, endDate time.Time) ([]model.TrafficUsage, error) {
	var traffic []model.TrafficUsage
	result := database.DB.Where("user_id = ? AND date BETWEEN ? AND ?", userID, startDate, endDate).
		Order("date DESC").Find(&traffic)
	return traffic, result.Error
}

// GetMonthlyTrafficUsage returns traffic usage for current month
func (s *TrafficService) GetMonthlyTrafficUsage(userID uint) (int64, error) {
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, -1)

	var totalBytes int64
	result := database.DB.Model(&model.TrafficUsage{}).
		Where("user_id = ? AND date BETWEEN ? AND ?", userID, startOfMonth, endOfMonth).
		Select("COALESCE(SUM(bytes), 0)").
		Scan(&totalBytes)

	return totalBytes, result.Error
}

// CheckTrafficLimit checks if user has exceeded their traffic limit
func (s *TrafficService) CheckTrafficLimit(userID uint) (bool, error) {
	var user model.User
	result := database.DB.Preload("Service").First(&user, userID)
	if result.Error != nil {
		return true, result.Error // Assume exceeded on error
	}

	if user.Service == nil {
		return true, nil // No service = no access
	}

	monthlyBytes, err := s.GetMonthlyTrafficUsage(userID)
	if err != nil {
		return true, err
	}

	monthlyMB := monthlyBytes / (1024 * 1024)
	return monthlyMB >= user.Service.TrafficMB, nil
}

// GetTrafficStats returns traffic statistics for admin dashboard
func (s *TrafficService) GetTrafficStats() (map[string]interface{}, error) {
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	startOfLastMonth := startOfMonth.AddDate(0, -1, 0)

	// Current month traffic
	var currentMonthBytes int64
	database.DB.Model(&model.TrafficUsage{}).
		Where("date >= ?", startOfMonth).
		Select("COALESCE(SUM(bytes), 0)").
		Scan(&currentMonthBytes)

	// Last month traffic
	var lastMonthBytes int64
	database.DB.Model(&model.TrafficUsage{}).
		Where("date >= ? AND date < ?", startOfLastMonth, startOfMonth).
		Select("COALESCE(SUM(bytes), 0)").
		Scan(&lastMonthBytes)

	// Top users by traffic this month
	type UserTraffic struct {
		UserID   uint   `json:"user_id"`
		Username string `json:"username"`
		Email    string `json:"email"`
		Bytes    int64  `json:"bytes"`
	}

	var topUsers []UserTraffic
	database.DB.Table("traffic_usages").
		Select("traffic_usages.user_id, users.username, users.email, SUM(traffic_usages.bytes) as bytes").
		Joins("JOIN users ON users.id = traffic_usages.user_id").
		Where("traffic_usages.date >= ?", startOfMonth).
		Group("traffic_usages.user_id, users.username, users.email").
		Order("bytes DESC").
		Limit(10).
		Scan(&topUsers)

	stats := map[string]interface{}{
		"current_month_gb": float64(currentMonthBytes) / (1024 * 1024 * 1024),
		"last_month_gb":    float64(lastMonthBytes) / (1024 * 1024 * 1024),
		"top_users":        topUsers,
		"last_updated":     time.Now(),
	}

	return stats, nil
}

// CleanupOldTraffic removes traffic records older than specified months
func (s *TrafficService) CleanupOldTraffic(monthsToKeep int) error {
	cutoffDate := time.Now().AddDate(0, -monthsToKeep, 0)
	return database.DB.Where("date < ?", cutoffDate).Delete(&model.TrafficUsage{}).Error
}

// GetDailyTrafficChart returns daily traffic data for charts
func (s *TrafficService) GetDailyTrafficChart(days int) ([]map[string]interface{}, error) {
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -days)

	type DailyTraffic struct {
		Date  time.Time `json:"date"`
		Bytes int64     `json:"bytes"`
	}

	var dailyTraffic []DailyTraffic
	result := database.DB.Model(&model.TrafficUsage{}).
		Select("DATE(date) as date, SUM(bytes) as bytes").
		Where("date >= ?", startDate).
		Group("DATE(date)").
		Order("date").
		Scan(&dailyTraffic)

	if result.Error != nil {
		return nil, result.Error
	}

	// Convert to chart format
	var chartData []map[string]interface{}
	for _, traffic := range dailyTraffic {
		chartData = append(chartData, map[string]interface{}{
			"date": traffic.Date.Format("2006-01-02"),
			"mb":   float64(traffic.Bytes) / (1024 * 1024),
		})
	}

	return chartData, nil
}
