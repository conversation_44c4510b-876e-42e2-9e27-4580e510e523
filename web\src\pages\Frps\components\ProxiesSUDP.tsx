import { frpsAPI } from "@/utils/api";
import { SUDPProxy as SUDPProxyUtil } from "@/utils/proxy";
import { message } from "antd";
import React, { useEffect, useState } from "react";
import ProxyTable from "../components/ProxyTable";

interface ProxiesSUDPProps {
  nodeId?: number;
}

const ProxiesSUDP: React.FC<ProxiesSUDPProps> = ({ nodeId }) => {
  const [loading, setLoading] = useState(false);
  const [proxies, setProxies] = useState<any[]>([]);

  useEffect(() => {
    if (nodeId) {
      fetchData();
    } else {
      setProxies([]);
    }
  }, [nodeId]);

  const fetchData = async () => {
    if (!nodeId) return;

    setLoading(true);
    try {
      const proxyResponse = await frpsAPI.getProxies("sudp", nodeId);
      const proxyData = proxyResponse.data;

      if (proxyData?.proxies) {
        const sudpProxies = proxyData.proxies.map(
          (proxy: any) => new SUDPProxyUtil(proxy)
        );
        setProxies(sudpProxies);
      } else {
        setProxies([]);
      }
    } catch (error) {
      message.error("获取SUDP代理数据失败");
      console.error("Error fetching SUDP proxies:", error);
      setProxies([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <ProxyTable
        proxies={proxies}
        proxyType="sudp"
        loading={loading}
        onRefresh={fetchData}
      />
    </div>
  );
};

export default ProxiesSUDP;
