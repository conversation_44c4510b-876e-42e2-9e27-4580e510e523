package model

import (
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// DomainStatus represents the status of a domain
type DomainStatus string

const (
	DomainStatusActive   DomainStatus = "active"
	DomainStatusInactive DomainStatus = "inactive"
	DomainStatusPending  DomainStatus = "pending"
	DomainStatusError    DomainStatus = "error"
)

// Domain represents a domain configuration for a user on a specific node
type Domain struct {
	ID          uint           `gorm:"primarykey" json:"id"`
	Domain      string         `gorm:"uniqueIndex;not null;size:255;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"domain"`
	UserID      uint           `gorm:"not null;index" json:"user_id"`
	User        User           `gorm:"foreignKey:UserID" json:"user,omitempty"`
	NodeID      uint           `gorm:"not null;index" json:"node_id"`
	Node        FrpsNode       `gorm:"foreignKey:NodeID" json:"node,omitempty"`
	Status      DomainStatus   `gorm:"default:'pending';size:20" json:"status"`
	Description string         `gorm:"type:text;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"description"`
	IsActive    bool           `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

// GenerateDomain generates a domain name based on username, node name and base domain
func (d *Domain) GenerateDomain(username, nodeName, baseDomain string) string {
	return fmt.Sprintf("%s.%s.%s", username, nodeName, baseDomain)
}

// ParseDomain parses a domain to extract username and node name
func ParseDomain(domain, baseDomain string) (username, nodeName string, err error) {
	if !strings.HasSuffix(domain, "."+baseDomain) {
		return "", "", fmt.Errorf("domain does not match base domain")
	}

	// Remove base domain suffix
	prefix := strings.TrimSuffix(domain, "."+baseDomain)
	parts := strings.Split(prefix, ".")

	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid domain format, expected username.nodename.%s", baseDomain)
	}

	return parts[0], parts[1], nil
}

// IsValidDomainFormat checks if the domain follows the expected format
func (d *Domain) IsValidDomainFormat(baseDomain string) bool {
	if d.User.ID == 0 || d.Node.ID == 0 {
		return false
	}

	expectedDomain := d.GenerateDomain(d.User.Username, d.Node.Name, baseDomain)
	return d.Domain == expectedDomain
}

// Activate activates the domain
func (d *Domain) Activate() {
	d.Status = DomainStatusActive
	d.IsActive = true
}

// Deactivate deactivates the domain
func (d *Domain) Deactivate() {
	d.Status = DomainStatusInactive
	d.IsActive = false
}

// SetError sets the domain status to error with a message
func (d *Domain) SetError(errorMsg string) {
	d.Status = DomainStatusError
	d.Description = errorMsg
	d.IsActive = false
}
