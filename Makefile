export GO111MODULE=on
LDFLAGS := -s -w

all: fp

fp:
	go build -o ./bin/fp ./cmd/

build:
	go build ./...

.PHONY: web
web:
	cd web && npm install && npm run build

frps:
	cd frp && env CGO_ENABLED=0 go build -trimpath -ldflags "$(LDFLAGS)" -tags frps -o ../bin/frps ./cmd/frps

frpc:
	cd frp && env CGO_ENABLED=0 go build -trimpath -ldflags "$(LDFLAGS)" -tags frpc -o ../bin/frpc ./cmd/frpc

run-frps:
	./bin/frps -c ./conf/frps.toml

run-frpc:
	./bin/frpc -c ./conf/frpc.toml

run-server:
	go run ./cmd/

run-web:
	cd web && npm run dev

cross-compile:
	env CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-darwin-amd64 ./cmd/
	env CGO_ENABLED=0 GOOS=freebsd GOARCH=386 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-freebsd-386 ./cmd/
	env CGO_ENABLED=0 GOOS=freebsd GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-freebsd-amd64 ./cmd/
	env CGO_ENABLED=0 GOOS=linux GOARCH=386 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-linux-386 ./cmd/
	env CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-linux-amd64 ./cmd/
	env CGO_ENABLED=0 GOOS=linux GOARCH=arm go build -ldflags "$(LDFLAGS)" -o ./bin/fp-linux-arm ./cmd/
	env CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-linux-arm64 ./cmd/
	env CGO_ENABLED=0 GOOS=windows GOARCH=386 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-windows-386.exe ./cmd/
	env CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-windows-amd64.exe ./cmd/
	env CGO_ENABLED=0 GOOS=linux GOARCH=mips64 go build -ldflags "$(LDFLAGS)" -o ./bin/fp-linux-mips64 ./cmd/
	env CGO_ENABLED=0 GOOS=linux GOARCH=mips64le go build -ldflags "$(LDFLAGS)" -o ./bin/fp-linux-mips64le ./cmd/
	env CGO_ENABLED=0 GOOS=linux GOARCH=mips GOMIPS=softfloat go build -ldflags "$(LDFLAGS)" -o ./bin/fp-linux-mips ./cmd/
	env CGO_ENABLED=0 GOOS=linux GOARCH=mipsle GOMIPS=softfloat go build -ldflags "$(LDFLAGS)" -o ./bin/fp-linux-mipsle ./cmd/