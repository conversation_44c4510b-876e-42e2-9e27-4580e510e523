# FRP Desktop Client 启动指南

## 🚨 重要提醒

在启动应用之前，请确保已正确配置 FRPC 二进制文件！

## 📋 启动前检查

### 1. 检查 FRPC 二进制文件

```bash
cd frp-desktop
npm run check-frpc
```

如果显示 "❌ 未找到 FRPC 二进制文件"，请按照以下步骤操作：

### 2. 下载并配置 FRPC

1. **下载 FRP**
   - 访问: https://github.com/fatedier/frp/releases
   - 下载适合您系统的版本

2. **解压并复制文件**
   ```bash
   # 创建 bin 目录（如果不存在）
   mkdir -p bin
   
   # 复制 frpc 文件到 bin 目录
   # Windows:
   copy frpc.exe bin/
   
   # Linux/macOS:
   cp frpc bin/
   chmod +x bin/frpc
   ```

3. **验证安装**
   ```bash
   npm run check-frpc
   ```

## 🚀 启动应用

### 方法一：使用启动脚本（推荐）

**Windows:**
```bash
start.bat
```

**Linux/macOS:**
```bash
chmod +x start.sh
./start.sh
```

### 方法二：手动启动

```bash
# 1. 安装依赖
npm install

# 2. 检查 FRPC 二进制文件
npm run check-frpc

# 3. 启动开发服务器
npm run electron:dev
```

## 🔧 使用说明

### 1. 启动后端服务
确保后端服务运行在 `http://localhost:7200`

### 2. 登录应用
- 首次使用请注册新账户
- 已有账户直接登录

### 3. 测试 FRPC 二进制文件
- 在控制台页面点击"测试二进制"按钮
- 确认 FRPC 文件可以正常执行

### 4. 配置 FRPC
- 点击"配置管理"
- 填写服务器信息
- 添加隧道配置

### 5. 启动服务
- 返回控制台
- 点击"启动 FRPC 服务"按钮
- 观察日志输出

## 🐛 故障排除

### 问题1: "FRPC binary not found"
**解决方案:**
1. 运行 `npm run check-frpc` 检查文件位置
2. 确保 frpc 文件在正确的 bin/ 目录下
3. 检查文件名是否正确（Windows: frpc.exe, Linux/macOS: frpc）

### 问题2: "Permission denied"
**解决方案 (Linux/macOS):**
```bash
chmod +x bin/frpc
```

### 问题3: 启动后没有日志输出
**可能原因:**
1. 配置文件路径错误
2. 配置文件格式错误
3. 服务器连接失败

**解决方案:**
1. 检查配置文件是否存在
2. 验证服务器地址和端口
3. 查看应用日志获取详细错误信息

### 问题4: 无法停止服务
**解决方案:**
1. 点击"停止服务"按钮
2. 如果无效，重启应用
3. 手动杀死 frpc 进程

## 📝 日志查看

- **实时日志**: 在"日志查看"页面查看 FRPC 实时输出
- **应用日志**: 查看应用本身的运行日志
- **搜索过滤**: 使用搜索功能快速定位问题

## 🆘 获取帮助

如果仍有问题：
1. 查看控制台错误信息
2. 检查应用日志
3. 确认网络连接
4. 联系技术支持

---

**记住**: 启动服务前一定要先测试 FRPC 二进制文件！
