import React, { useEffect, useMemo, useState } from "react";
import { Layout as AntLayout, Menu, Dropdown, But<PERSON>, theme } from "antd";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import {
  DashboardOutlined,
  TeamOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  UserOutlined,
  LogoutOutlined,
  DatabaseOutlined,
  ApiOutlined,
  SafetyCertificateOutlined,
  DownOutlined,
  AppstoreOutlined,
  CloudServerOutlined,
  LineChartOutlined,
} from "@ant-design/icons";
import { adminAPI } from "@/utils/api";
import type { MenuProps } from "antd";

const { Header, Content } = AntLayout;

const Layout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [role, setRole] = useState<string>("admin");
  const [username, setUsername] = useState<string>("管理员");
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    adminAPI
      .getProfile()
      .then((res: { data?: { role?: string; username?: string } }) => {
        setRole(res.data?.role || "admin");
        setUsername(res.data?.username || "管理员");
      })
      .catch(() => {
        // keep defaults
      });
  }, []);

  const menuItems = useMemo<MenuProps["items"]>(() => {
    const allMenuItems: any[] = [
      {
        key: "/",
        icon: <DashboardOutlined />,
        label: "仪表盘",
        roles: ["admin", "user"],
      },
      {
        key: "admin",
        icon: <TeamOutlined />,
        label: "系统管理",
        roles: ["admin"],
        children: [
          {
            key: "/users",
            label: "用户管理",
            icon: <TeamOutlined />,
          },
          {
            key: "/rbac",
            label: "权限管理",
            icon: <SafetyCertificateOutlined />,
          },
        ],
      },
      {
        key: "frps",
        icon: <CloudServerOutlined />,
        label: "FRPS 管理",
        roles: ["admin"],
        children: [
          {
            key: "/frps-nodes",
            label: "节点管理",
            icon: <CloudServerOutlined />,
          },
          {
            key: "/services",
            label: "服务管理",
            icon: <SettingOutlined />,
          },
        ],
      },
      {
        key: "/subscriptions",
        icon: <AppstoreOutlined />,
        label: "订阅管理",
        roles: ["admin", "user"],
      },
      {
        key: "monitor",
        icon: <LineChartOutlined />,
        label: "监控统计",
        roles: ["admin", "user"],
        children: [
          {
            key: "/traffic",
            label: "流量统计",
            icon: <BarChartOutlined />,
          },
          ...(role === "admin"
            ? [
                {
                  key: "/stats",
                  label: "系统监控",
                  icon: <BarChartOutlined />,
                },
              ]
            : []),
        ],
      },
    ];

    return allMenuItems
      .filter((item) => item.roles.includes(role) || role === "admin")
      .map(({ roles, ...rest }) => rest);
  }, [role]);

  const userMenuItems = [
    {
      key: "my-token",
      icon: <UserOutlined />,
      label: "我的令牌",
      onClick: () => navigate("/my-token"),
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "退出登录",
      onClick: () => {
        localStorage.removeItem("admin_token");
        navigate("/login");
      },
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const {
    token: { colorBgContainer },
  } = theme.useToken();

  return (
    <AntLayout className="layout" style={{ minHeight: "100vh" }}>
      <Header
        style={{
          position: "sticky",
          top: 0,
          zIndex: 1,
          width: "100%",
          display: "flex",
          alignItems: "center",
          padding: "0 24px",
          background: colorBgContainer,
          boxShadow: "0 1px 4px rgba(0,21,41,.08)",
        }}
      >
        <div
          className="logo"
          style={{
            float: "left",
            width: "200px",
            height: "31px",
            margin: "16px 24px 16px 0",
            fontWeight: "bold",
            fontSize: "18px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          FRP 多租户系统
        </div>

        <Menu
          mode="horizontal"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{
            flex: 1,
            minWidth: 0,
            background: "transparent",
            borderBottom: "none",
            lineHeight: "64px",
          }}
        />

        <div
          style={{ marginLeft: "auto", display: "flex", alignItems: "center" }}
        >
          <Dropdown menu={{ items: userMenuItems }} trigger={["click"]}>
            <Button type="text">
              <UserOutlined style={{ marginRight: 8 }} />
              {username}
              <DownOutlined style={{ marginLeft: 8 }} />
            </Button>
          </Dropdown>
        </div>
      </Header>

      <Content
        style={{
          margin: "24px 16px",
          padding: 24,
          minHeight: 280,
          background: colorBgContainer,
          borderRadius: 6,
        }}
      >
        <Outlet />
      </Content>
    </AntLayout>
  );
};

export default Layout;
