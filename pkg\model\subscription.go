package model

import "time"

// ServiceSubscription represents a user's subscription to a service
type ServiceSubscription struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	UserID    uint      `gorm:"not null;index" json:"user_id"`
	User      User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	ServiceID uint      `gorm:"not null;index" json:"service_id"`
	Service   Service   `gorm:"foreignKey:ServiceID" json:"service,omitempty"`
	StartDate time.Time `gorm:"not null" json:"start_date"`
	EndDate   time.Time `gorm:"not null" json:"end_date"`
	IsActive  bool      `gorm:"default:true" json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
