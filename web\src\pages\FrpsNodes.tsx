import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Card,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  message,
  Typography,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Statistic,
  Divider,
  Tabs,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
  EyeOutlined,
  PoweroffOutlined,
} from '@ant-design/icons';
import NodeDetailModal from '../components/NodeDetailModal';
import { frpsNodesAPI } from '../utils/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface FrpsNode {
  id: number;
  name: string;
  description: string;
  host: string;
  port: number;
  admin_port: number;
  admin_user: string;
  admin_pwd: string;
  status: 'online' | 'offline' | 'error' | 'unknown';
  is_active: boolean;
  last_check: string;
  error_msg: string;
  version: string;
  uptime: number;
  client_count: number;
  proxy_count: number;
  created_at: string;
  updated_at: string;
}

interface DashboardStats {
  total_nodes: number;
  online_nodes: number;
  offline_nodes: number;
  total_clients: number;
  total_proxies: number;
}

const FrpsNodes: React.FC = () => {
  const [nodes, setNodes] = useState<FrpsNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingNode, setEditingNode] = useState<FrpsNode | null>(null);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedNodeForDetail, setSelectedNodeForDetail] = useState<FrpsNode | null>(null);
  const [form] = Form.useForm();

  // 获取节点列表
  const fetchNodes = async () => {
    setLoading(true);
    try {
      const response = await frpsNodesAPI.getNodes();
      setNodes(response.data);
    } catch (error) {
      message.error('获取节点列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取仪表板统计
  const fetchDashboardStats = async () => {
    try {
      const response = await frpsNodesAPI.getDashboardStats();
      setDashboardStats(response.data);
    } catch (error) {
      console.error('获取仪表板统计失败:', error);
    }
  };

  useEffect(() => {
    fetchNodes();
    fetchDashboardStats();
  }, []);

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      online: { color: 'green', icon: <CheckCircleOutlined />, text: '在线' },
      offline: { color: 'red', icon: <CloseCircleOutlined />, text: '离线' },
      error: { color: 'orange', icon: <ExclamationCircleOutlined />, text: '错误' },
      unknown: { color: 'gray', icon: <QuestionCircleOutlined />, text: '未知' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.unknown;
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 检查节点状态
  const checkNodeStatus = async (id: number) => {
    try {
      await frpsNodesAPI.checkNodeStatus(id);
      message.success('节点状态检查完成');
      fetchNodes();
    } catch (error) {
      message.error('检查节点状态失败');
    }
  };

  // 检查所有节点状态
  const checkAllNodes = async () => {
    setLoading(true);
    try {
      await frpsNodesAPI.checkAllNodes();
      message.success('所有节点状态检查完成');
      fetchNodes();
      fetchDashboardStats();
    } catch (error) {
      message.error('检查所有节点状态失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换节点状态
  const toggleNodeStatus = async (id: number) => {
    try {
      await frpsNodesAPI.toggleNodeStatus(id);
      message.success('节点状态切换成功');
      fetchNodes();
      fetchDashboardStats();
    } catch (error) {
      message.error('切换节点状态失败');
    }
  };

  // 删除节点
  const deleteNode = async (id: number) => {
    try {
      await frpsNodesAPI.deleteNode(id);
      message.success('节点删除成功');
      fetchNodes();
      fetchDashboardStats();
    } catch (error) {
      message.error('删除节点失败');
    }
  };

  // 打开编辑/新增模态框
  const openModal = (node?: FrpsNode) => {
    setEditingNode(node || null);
    setModalVisible(true);
    if (node) {
      form.setFieldsValue(node);
    } else {
      form.resetFields();
    }
  };

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false);
    setEditingNode(null);
    form.resetFields();
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      if (editingNode) {
        await frpsNodesAPI.updateNode(editingNode.id, values);
        message.success('节点更新成功');
      } else {
        await frpsNodesAPI.createNode(values);
        message.success('节点创建成功');
      }
      closeModal();
      fetchNodes();
      fetchDashboardStats();
    } catch (error) {
      message.error(editingNode ? '更新节点失败' : '创建节点失败');
    }
  };

  // 打开节点详情模态框
  const openDetailModal = (node: FrpsNode) => {
    setSelectedNodeForDetail(node);
    setDetailModalVisible(true);
  };

  // 关闭节点详情模态框
  const closeDetailModal = () => {
    setDetailModalVisible(false);
    setSelectedNodeForDetail(null);
  };

  // 表格列定义
  const columns = [
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: FrpsNode) => (
        <div>
          <Text strong>{text}</Text>
          {record.description && (
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.description}
              </Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '地址',
      key: 'address',
      render: (record: FrpsNode) => (
        <div>
          <div>{record.host}:{record.port}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            管理端口: {record.admin_port}
          </Text>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: FrpsNode) => (
        <div>
          {renderStatusTag(status)}
          {!record.is_active && (
            <Tag color="gray" style={{ marginTop: 4 }}>
              已禁用
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '统计信息',
      key: 'stats',
      render: (record: FrpsNode) => (
        <div>
          <div>客户端: {record.client_count}</div>
          <div>代理: {record.proxy_count}</div>
          {record.version && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              版本: {record.version}
            </Text>
          )}
        </div>
      ),
    },
    {
      title: '最后检查',
      dataIndex: 'last_check',
      key: 'last_check',
      render: (lastCheck: string, record: FrpsNode) => (
        <div>
          {lastCheck ? (
            <div>
              <div>{new Date(lastCheck).toLocaleString()}</div>
              {record.error_msg && (
                <Tooltip title={record.error_msg}>
                  <Text type="danger" style={{ fontSize: '12px' }}>
                    错误信息
                  </Text>
                </Tooltip>
              )}
            </div>
          ) : (
            <Text type="secondary">未检查</Text>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: FrpsNode) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => openDetailModal(record)}
              disabled={record.status !== 'online'}
            />
          </Tooltip>
          <Tooltip title="检查状态">
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={() => checkNodeStatus(record.id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openModal(record)}
            />
          </Tooltip>
          <Tooltip title={record.is_active ? '禁用' : '启用'}>
            <Button
              type="text"
              icon={<PoweroffOutlined />}
              onClick={() => toggleNodeStatus(record.id)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个节点吗？"
            onConfirm={() => deleteNode(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>FRPS 节点管理</Title>

      {/* 统计卡片 */}
      {dashboardStats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="总节点数" value={dashboardStats.total_nodes} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="在线节点"
                value={dashboardStats.online_nodes}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="总客户端" value={dashboardStats.total_clients} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="总代理" value={dashboardStats.total_proxies} />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openModal()}
            >
              添加节点
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={checkAllNodes}
              loading={loading}
            >
              检查所有节点
            </Button>
            <Button icon={<ReloadOutlined />} onClick={fetchNodes}>
              刷新列表
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={nodes}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个节点`,
          }}
        />
      </Card>

      {/* 编辑/新增模态框 */}
      <Modal
        title={editingNode ? '编辑节点' : '添加节点'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true, admin_port: 7500, port: 7000 }}
        >
          <Form.Item
            name="name"
            label="节点名称"
            rules={[{ required: true, message: '请输入节点名称' }]}
          >
            <Input placeholder="请输入节点名称" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={2} placeholder="请输入节点描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="host"
                label="主机地址"
                rules={[{ required: true, message: '请输入主机地址' }]}
              >
                <Input placeholder="例如: *************" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="port"
                label="服务端口"
                rules={[{ required: true, message: '请输入服务端口' }]}
              >
                <InputNumber
                  min={1}
                  max={65535}
                  style={{ width: '100%' }}
                  placeholder="7000"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="admin_port"
                label="管理端口"
                rules={[{ required: true, message: '请输入管理端口' }]}
              >
                <InputNumber
                  min={1}
                  max={65535}
                  style={{ width: '100%' }}
                  placeholder="7500"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="admin_user" label="管理用户">
                <Input placeholder="admin" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="admin_pwd" label="管理密码">
                <Input.Password placeholder="请输入管理密码" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="is_active" label="启用状态" valuePropName="checked">
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingNode ? '更新' : '创建'}
              </Button>
              <Button onClick={closeModal}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 节点详情模态框 */}
      <NodeDetailModal
        visible={detailModalVisible}
        node={selectedNodeForDetail}
        onClose={closeDetailModal}
      />
    </div>
  );
};

export default FrpsNodes;
