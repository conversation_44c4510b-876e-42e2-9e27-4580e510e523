import { ConfigProvider } from "antd";
import zhC<PERSON> from "antd/locale/zh_CN";
import React from "react";
import { Route, BrowserRouter as Router, Routes } from "react-router-dom";
// 导入样式
import "./App.css";
// 导入组件
import Layout from "./components/Layout";
import Dashboard from "./pages/Dashboard";
import FrpsOverview from "./pages/Frps/OverView";
import FrpsProxies from "./pages/Frps/Proxies";
import FrpsNodes from "./pages/FrpsNodes";
import Login from "./pages/Login";
import Services from "./pages/Services";
import Stats from "./pages/Stats";
import MyToken from "./pages/MyToken";
import Traffic from "./pages/Traffic";
import Users from "./pages/Users";
import RBAC from "./pages/RBAC";
import Subscription from "./pages/Subscription";
import Domains from "./pages/Domains";
import Subscriptions from "./pages/Subscriptions";

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="users" element={<Users />} />
            <Route path="services" element={<Services />} />
            <Route path="traffic" element={<Traffic />} />
            <Route path="stats" element={<Stats />} />
            <Route path="frps-overview" element={<FrpsOverview />} />
            <Route path="frps-proxies" element={<FrpsProxies />} />
            <Route path="frps-nodes" element={<FrpsNodes />} />
            <Route path="domains" element={<Domains />} />
            <Route path="rbac" element={<RBAC />} />
            <Route path="my-token" element={<MyToken />} />
            <Route path="subscription" element={<Subscription />} />
            <Route path="subscriptions" element={<Subscriptions />} />
          </Route>
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

export default App;
