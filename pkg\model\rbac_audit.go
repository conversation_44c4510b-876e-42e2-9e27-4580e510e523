package model

import "time"

// RBACAudit logs changes to RBAC policies and role assignments
// Action: policy_add, policy_remove, role_add, role_remove, reload
// Detail: JSON string describing the change
// Actor: username or admin identifier
// Time fields are auto-managed by GORM

type RBACAudit struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Action    string    `gorm:"index;size:32;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"action"`
	Detail    string    `gorm:"type:text;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"detail"`
	Actor     string    `gorm:"size:128;index;charset:utf8mb4;collate:utf8mb4_unicode_ci" json:"actor"`
	CreatedAt time.Time `json:"created_at"`
}
