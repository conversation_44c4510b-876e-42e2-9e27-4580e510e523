package model

import "time"

// TrafficUsage tracks user's traffic consumption
type TrafficUsage struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	UserID    uint      `gorm:"not null;index" json:"user_id"`
	User      User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Bytes     int64     `gorm:"not null" json:"bytes"`
	Date      time.Time `gorm:"not null;index" json:"date"`
	CreatedAt time.Time `json:"created_at"`
}
