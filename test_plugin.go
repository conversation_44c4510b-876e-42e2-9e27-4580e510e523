package main

import (
	"fmt"
	"log"

	"frp-panel/pkg/config"
	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
	"frp-panel/pkg/service"
)

func main() {
	// Initialize database
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Type: "sqlite",
			DSN:  "./users.db",
		},
	}

	if err := database.Init(cfg.Database.DSN); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Create a test user
	authService := service.NewAuthService("")

	// Check if user already exists
	var existingUser model.User
	result := database.DB.Where("username = ?", "testuser").First(&existingUser)
	if result.Error == nil {
		fmt.Printf("User already exists. Token: %s\n", existingUser.Token)
		return
	}

	// Create new user
	user, err := authService.Register("testuser", "<EMAIL>", "testpass")
	if err != nil {
		log.Fatal("Failed to create user:", err)
	}

	fmt.Printf("Created user: %s\n", user.Username)
	fmt.Printf("Token: %s\n", user.Token)
	fmt.Printf("Email: %s\n", user.Email)
}
