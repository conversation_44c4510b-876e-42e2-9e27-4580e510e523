package controller

import (
	"errors"
	"net/http"
	"time"

	"frp-panel/pkg/database"
	"frp-panel/pkg/model"
	"frp-panel/pkg/service"

	"github.com/gin-gonic/gin"
)

var (
	ErrUnauthorized  = errors.New("unauthorized")
	ErrInvalidUserID = errors.New("invalid user ID")
)

type SubscriptionController struct {
	userService *service.UserService
}

func NewSubscriptionController(userService *service.UserService) *SubscriptionController {
	return &SubscriptionController{
		userService: userService,
	}
}

// PurchaseService handles the purchase of a service by a user
func (c *SubscriptionController) PurchaseService(ctx *gin.Context) (interface{}, error) {
	userID, err := getUserIDFromContext(ctx)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusUnauthorized,
			Err:  err,
		}
	}

	var req struct {
		ServiceID uint `json:"service_id" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		return nil, &HTTPError{
			Code: http.StatusBadRequest,
			Err:  err,
		}
	}

	// Start a transaction
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get the service
	var service model.Service
	if err := tx.First(&service, req.ServiceID).Error; err != nil {
		tx.Rollback()
		return nil, &HTTPError{
			Code: http.StatusNotFound,
			Err:  err,
		}
	}

	// Get the user with their current subscription
	var user model.User
	if err := tx.Preload("Service").First(&user, userID).Error; err != nil {
		tx.Rollback()
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	// Calculate subscription dates
	now := time.Now()
	endDate := now.AddDate(0, 1, 0) // 1 month from now

	// Create new subscription
	subscription := model.ServiceSubscription{
		UserID:    userID,
		ServiceID: req.ServiceID,
		StartDate: now,
		EndDate:   endDate,
		IsActive:  true,
	}

	if err := tx.Create(&subscription).Error; err != nil {
		tx.Rollback()
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	// Update user's current service
	user.ServiceID = &req.ServiceID
	if err := tx.Save(&user).Error; err != nil {
		tx.Rollback()
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return gin.H{
		"message":      "Service purchased successfully",
		"subscription": subscription,
	}, nil
}

// GetUserSubscriptions returns all subscriptions for the current user
func (c *SubscriptionController) GetUserSubscriptions(ctx *gin.Context) (interface{}, error) {
	userID, err := getUserIDFromContext(ctx)
	if err != nil {
		return nil, &HTTPError{
			Code: http.StatusUnauthorized,
			Err:  err,
		}
	}

	var subscriptions []model.ServiceSubscription
	if err := database.DB.Preload("Service").Where("user_id = ?", userID).Order("end_date DESC").Find(&subscriptions).Error; err != nil {
		return nil, &HTTPError{
			Code: http.StatusInternalServerError,
			Err:  err,
		}
	}

	return subscriptions, nil
}

// getUserIDFromContext extracts the user ID from the context (assuming JWT middleware sets it)
func getUserIDFromContext(ctx *gin.Context) (uint, error) {
	userID, exists := ctx.Get("user")
	if !exists {
		return 0, ErrUnauthorized
	}

	user, ok := userID.(*model.User)
	if !ok {
		return 0, ErrUnauthorized
	}

	if user.ID == 0 {
		return 0, ErrInvalidUserID
	}

	return user.ID, nil
}
